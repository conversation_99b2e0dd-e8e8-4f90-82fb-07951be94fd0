{"extension.displayName": "Roo Code", "extension.description": "Ein komplettes KI-Agenten-Entwicklungsteam in deinem Editor.", "command.newTask.title": "Neue Aufgabe", "command.explainCode.title": "Code Erklären", "command.fixCode.title": "Code Reparieren", "command.improveCode.title": "Code Verbessern", "command.addToContext.title": "Zum Kontext Hinzufügen", "command.openInNewTab.title": "In Neuem <PERSON>", "command.focusInput.title": "Eingabefeld Fokussieren", "command.setCustomStoragePath.title": "Benutzerdefinierten Speicherpfad Festlegen", "command.importSettings.title": "Einstellungen Importieren", "command.terminal.addToContext.title": "Terminal-Inhalt zum Kontext Hinzufügen", "command.terminal.fixCommand.title": "<PERSON><PERSON> Befehl Reparieren", "command.terminal.explainCommand.title": "Diesen Befehl Erklären", "command.acceptInput.title": "Eingabe/Vorschlag Akzeptieren", "views.activitybar.title": "Roo Code", "views.contextMenu.label": "Roo Code", "views.terminalMenu.label": "Roo Code", "views.sidebar.name": "Roo Code", "command.mcpServers.title": "MCP Server", "command.prompts.title": "<PERSON><PERSON>", "command.history.title": "<PERSON><PERSON><PERSON><PERSON>", "command.marketplace.title": "Marktplatz", "command.openInEditor.title": "Im Editor <PERSON>", "command.settings.title": "Einstellungen", "command.documentation.title": "Dokumentation", "configuration.title": "Roo Code", "commands.allowedCommands.description": "<PERSON><PERSON><PERSON><PERSON>, die automatisch ausgeführt werden können, wenn 'Ausführungsoperationen immer genehmigen' aktiviert ist", "commands.deniedCommands.description": "<PERSON><PERSON><PERSON>sp<PERSON><PERSON><PERSON><PERSON>, die automatisch abgelehnt werden, ohne nach Genehmigung zu fragen. Bei Konflikten mit erlaubten Befehlen hat die längste Präfix-Übereinstimmung Vorrang. Füge * hinzu, um alle Befehle abzulehnen.", "commands.commandExecutionTimeout.description": "Maximale Zeit in Sekunden, die auf den Abschluss der Befehlsausführung gewartet wird, bevor ein Timeout auftritt (0 = kein Timeout, 1-600s, Standard: 0s)", "commands.commandTimeoutAllowlist.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, die vom Timeout der Befehlsausführung ausgeschlossen sind. <PERSON><PERSON><PERSON><PERSON>, die diesen Präfixen entsprechen, werden ohne Timeout-Beschränkungen ausgeführt.", "settings.vsCodeLmModelSelector.description": "Einstellungen für die VSCode-Sprachmodell-API", "settings.vsCodeLmModelSelector.vendor.description": "Der Anbieter des Sprachmodells (z.B. copilot)", "settings.vsCodeLmModelSelector.family.description": "Die Familie des Sprachmodells (z.B. gpt-4)", "settings.customStoragePath.description": "Benutzerdefinierter Speicherpfad. <PERSON><PERSON>, um den Standardspeicherort zu verwenden. Unterstützt absolute Pfade (z.B. 'D:\\RooCodeStorage')", "settings.enableCodeActions.description": "Roo Code Schnelle Problembehebung aktivieren.", "settings.autoImportSettingsPath.description": "Pfad zu einer RooCode-Konfigurationsdatei, die beim Start der Erweiterung automatisch importiert wird. Unterstützt absolute Pfade und Pfade relativ zum Home-Verzeichnis (z.B. '~/Documents/roo-code-settings.json'). <PERSON><PERSON> <PERSON>, um den automatischen Import zu deaktivieren.", "settings.useAgentRules.description": "Aktiviert das Laden von AGENTS.md-Dateien für agentenspezifische Regeln (siehe https://agent-rules.org/)"}