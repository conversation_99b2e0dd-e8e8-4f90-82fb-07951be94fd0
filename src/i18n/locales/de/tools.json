{"readFile": {"linesRange": " (<PERSON><PERSON><PERSON> {{start}}-{{end}})", "definitionsOnly": " (nur Definitionen)", "maxLines": " (maximal {{max}} <PERSON><PERSON><PERSON>)", "imageTooLarge": "Die Bilddatei ist zu groß ({{size}} MB). Die maximal erlaubte Größe beträgt {{max}} MB.", "imageWithSize": "Bilddatei ({{size}} KB)"}, "toolRepetitionLimitReached": "<PERSON><PERSON> scheint in einer Schleife festzustecken und versucht wiederholt dieselbe Aktion ({{toolName}}). Dies könnte auf ein Problem mit der aktuellen Strategie hindeuten. Überlege dir, die Aufgabe umzuformulieren, genauere Anweisungen zu geben oder Roo zu einem anderen Ansatz zu führen.", "codebaseSearch": {"approval": "Suche nach '{{query}}' im Codebase..."}, "newTask": {"errors": {"policy_restriction": "Neue Aufgabe konnte aufgrund von Richtlinienbeschränkungen nicht erstellt werden."}}}