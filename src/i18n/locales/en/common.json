{"extension": {"name": "Roo Code", "description": "A whole dev team of AI agents in your editor."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "welcome": "Welcome, {{name}}! You have {{count}} notifications.", "items": {"zero": "No items", "one": "One item", "other": "{{count}} items"}, "confirmation": {"reset_state": "Are you sure you want to reset all state and secret storage in the extension? This cannot be undone.", "delete_config_profile": "Are you sure you want to delete this configuration profile?", "delete_custom_mode_with_rules": "Are you sure you want to delete this {scope} mode?\n\nThis will also delete the associated rules folder at:\n{rulesFolderPath}"}, "errors": {"invalid_data_uri": "Invalid data URI format", "error_copying_image": "Error copying image: {{errorMessage}}", "error_opening_image": "Error opening image: {{error}}", "error_saving_image": "Error saving image: {{errorMessage}}", "could_not_open_file": "Could not open file: {{errorMessage}}", "could_not_open_file_generic": "Could not open file!", "checkpoint_timeout": "Timed out when attempting to restore checkpoint.", "checkpoint_failed": "Failed to restore checkpoint.", "git_not_installed": "Git is required for the checkpoints feature. Please install Git to enable checkpoints.", "no_workspace": "Please open a project folder first", "update_support_prompt": "Failed to update support prompt", "reset_support_prompt": "Failed to reset support prompt", "enhance_prompt": "Failed to enhance prompt", "get_system_prompt": "Failed to get system prompt", "search_commits": "Failed to search commits", "save_api_config": "Failed to save api configuration", "create_api_config": "Failed to create api configuration", "rename_api_config": "Failed to rename api configuration", "load_api_config": "Failed to load api configuration", "delete_api_config": "Failed to delete api configuration", "list_api_config": "Failed to get list api configuration", "update_server_timeout": "Failed to update server timeout", "hmr_not_running": "Local development server is not running, HMR will not work. Please run 'npm run dev' before launching the extension to enable HMR.", "retrieve_current_mode": "Error: failed to retrieve current mode from state.", "failed_delete_repo": "Failed to delete associated shadow repository or branch: {{error}}", "failed_remove_directory": "Failed to remove task directory: {{error}}", "custom_storage_path_unusable": "Custom storage path \"{{path}}\" is unusable, will use default path", "cannot_access_path": "Cannot access path {{path}}: {{error}}", "settings_import_failed": "Settings import failed: {{error}}.", "mistake_limit_guidance": "This may indicate a failure in the model's thought process or inability to use a tool properly, which can be mitigated with some user guidance (e.g. \"Try breaking down the task into smaller steps\").", "violated_organization_allowlist": "Failed to run task: the current profile isn't compatible with your organization settings", "condense_failed": "Failed to condense context", "condense_not_enough_messages": "Not enough messages to condense context", "condensed_recently": "Context was condensed recently; skipping this attempt", "condense_handler_invalid": "API handler for condensing context is invalid", "condense_context_grew": "Context size increased during condensing; skipping this attempt", "url_timeout": "The website took too long to load (timeout). This could be due to a slow connection, heavy website, or the site being temporarily unavailable. You can try again later or check if the URL is correct.", "url_not_found": "The website address could not be found. Please check if the URL is correct and try again.", "no_internet": "No internet connection. Please check your network connection and try again.", "url_forbidden": "Access to this website is forbidden. The site may block automated access or require authentication.", "url_page_not_found": "The page was not found. Please check if the URL is correct.", "url_fetch_failed": "Failed to fetch URL content: {{error}}", "url_fetch_error_with_url": "Error fetching content for {{url}}: {{error}}", "command_timeout": "Command execution timed out after {{seconds}} seconds", "share_task_failed": "Failed to share task. Please try again.", "share_no_active_task": "No active task to share", "share_auth_required": "Authentication required. Please sign in to share tasks.", "share_not_enabled": "Task sharing is not enabled for this organization.", "share_task_not_found": "Task not found or access denied.", "mode_import_failed": "Failed to import mode: {{error}}", "delete_rules_folder_failed": "Failed to delete rules folder: {{rulesFolderPath}}. Error: {{error}}", "command_not_found": "Command '{{name}}' not found", "open_command_file": "Failed to open command file", "delete_command": "Failed to delete command", "no_workspace_for_project_command": "No workspace folder found for project command", "command_already_exists": "Command \"{{commandName}}\" already exists", "create_command_failed": "Failed to create command", "command_template_content": "---\ndescription: \"Brief description of what this command does\"\n---\n\nThis is a new slash command. Edit this file to customize the command behavior.", "claudeCode": {"processExited": "Claude Code process exited with code {{exitCode}}.", "errorOutput": "Error output: {{output}}", "processExitedWithError": "Claude Code process exited with code {{exitCode}}. Error output: {{output}}", "stoppedWithReason": "<PERSON> stopped with reason: {{reason}}", "apiKeyModelPlanMismatch": "API keys and subscription plans allow different models. Make sure the selected model is included in your plan.", "notFound": "Claude Code executable '{{claudePath}}' not found.\n\nPlease install Claude Code CLI:\n1. Visit {{installationUrl}} to download Claude Code\n2. Follow the installation instructions for your operating system\n3. Ensure the 'claude' command is available in your PATH\n4. Alternatively, configure a custom path in Roo settings under 'Claude Code Path'\n\nOriginal error: {{originalError}}"}, "gemini": {"generate_stream": "Gemini generate context stream error: {{error}}", "generate_complete_prompt": "Gemini completion error: {{error}}", "sources": "Sources:"}, "cerebras": {"authenticationFailed": "Cerebras API authentication failed. Please check your API key is valid and not expired.", "accessForbidden": "Cerebras API access forbidden. Your API key may not have access to the requested model or feature.", "rateLimitExceeded": "Cerebras API rate limit exceeded. Please wait before making another request.", "serverError": "Cerebras API server error ({{status}}). Please try again later.", "genericError": "Cerebras API Error ({{status}}): {{message}}", "noResponseBody": "Cerebras API Error: No response body", "completionError": "Cerebras completion error: {{error}}"}}, "warnings": {"no_terminal_content": "No terminal content selected", "missing_task_files": "This task's files are missing. Would you like to remove it from the task list?", "auto_import_failed": "Failed to auto-import RooCode settings: {{error}}"}, "info": {"no_changes": "No changes found.", "clipboard_copy": "System prompt successfully copied to clipboard", "history_cleanup": "Cleaned up {{count}} task(s) with missing files from history.", "custom_storage_path_set": "Custom storage path set: {{path}}", "default_storage_path": "Reverted to using default storage path", "settings_imported": "Settings imported successfully.", "auto_import_success": "RooCode settings automatically imported from {{filename}}", "share_link_copied": "Share link copied to clipboard", "organization_share_link_copied": "Organization share link copied to clipboard!", "public_share_link_copied": "Public share link copied to clipboard!", "image_copied_to_clipboard": "Image data URI copied to clipboard", "image_saved": "Image saved to {{path}}", "mode_exported": "Mode '{{mode}}' exported successfully", "mode_imported": "Mode imported successfully"}, "answers": {"yes": "Yes", "no": "No", "remove": "Remove", "keep": "Keep"}, "buttons": {"save": "Save", "edit": "Edit", "learn_more": "Learn More"}, "tasks": {"canceled": "Task error: It was stopped and canceled by the user.", "deleted": "Task failure: It was stopped and deleted by the user.", "incomplete": "Task #{{taskNumber}} (Incomplete)", "no_messages": "Task #{{taskNumber}} (No messages)"}, "storage": {"prompt_custom_path": "Enter custom conversation history storage path, leave empty to use default location", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "Please enter an absolute path (e.g. D:\\RooCodeStorage or /home/<USER>/storage)", "enter_valid_path": "Please enter a valid path"}, "input": {"task_prompt": "What should <PERSON><PERSON> do?", "task_placeholder": "Type your task here"}, "customModes": {"errors": {"yamlParseError": "Invalid YAML in .roomodes file at line {{line}}. Please check for:\n• Proper indentation (use spaces, not tabs)\n• Matching quotes and brackets\n• Valid YAML syntax", "schemaValidationError": "Invalid custom modes format in .roomodes:\n{{issues}}", "invalidFormat": "Invalid custom modes format. Please ensure your settings follow the correct YAML format.", "updateFailed": "Failed to update custom mode: {{error}}", "deleteFailed": "Failed to delete custom mode: {{error}}", "resetFailed": "Failed to reset custom modes: {{error}}", "modeNotFound": "Write error: <PERSON> not found", "noWorkspaceForProject": "No workspace folder found for project-specific mode", "rulesCleanupFailed": "Mode deleted successfully, but failed to delete rules folder at {{rulesFolderPath}}. You may need to delete it manually."}, "scope": {"project": "project", "global": "global"}}, "marketplace": {"mode": {"rulesCleanupFailed": "Mode removed successfully, but failed to delete rules folder at {{rulesFolderPath}}. You may need to delete it manually."}}, "mdm": {"errors": {"cloud_auth_required": "Your organization requires Roo Code Cloud authentication. Please sign in to continue.", "organization_mismatch": "You must be authenticated with your organization's Roo Code Cloud account.", "verification_failed": "Unable to verify organization authentication."}}, "prompts": {"deleteMode": {"title": "Delete Custom Mode", "description": "Are you sure you want to delete this {{scope}} mode? This will also delete the associated rules folder at: {{rulesFolderPath}}", "descriptionNoRules": "Are you sure you want to delete this custom mode?", "confirm": "Delete"}}, "commands": {"preventCompletionWithOpenTodos": {"description": "Prevent task completion when there are incomplete todos in the todo list"}}}