{"extension": {"name": "Roo Code", "description": "<PERSON><PERSON><PERSON> bộ đội ngũ phát triển AI trong trình soạn thảo của bạn."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "welcome": "<PERSON><PERSON>o mừng, {{name}}! Bạn có {{count}} thông báo.", "items": {"zero": "<PERSON><PERSON><PERSON><PERSON> có mục nào", "one": "<PERSON><PERSON><PERSON>", "other": "{{count}} mục"}, "confirmation": {"reset_state": "Bạn có chắc chắn muốn đặt lại tất cả trạng thái và lưu trữ bí mật trong tiện ích mở rộng không? Hành động này không thể hoàn tác.", "delete_config_profile": "Bạn có chắc chắn muốn xóa hồ sơ cấu hình này không?", "delete_custom_mode_with_rules": "Bạn có chắc chắn muốn xóa chế độ {scope} này không?\n\nThao tác này cũng sẽ xóa thư mục quy tắc liên quan tại:\n{rulesFolderPath}"}, "errors": {"invalid_data_uri": "<PERSON><PERSON><PERSON> dạng URI dữ liệu không hợp lệ", "error_copying_image": "Lỗi khi sao chép hình ảnh: {{errorMessage}}", "error_saving_image": "Lỗi khi lưu hình <PERSON>nh: {{errorMessage}}", "error_opening_image": "Lỗi khi mở hình ảnh: {{error}}", "could_not_open_file": "<PERSON><PERSON><PERSON><PERSON> thể mở tệp: {{errorMessage}}", "could_not_open_file_generic": "<PERSON>h<PERSON>ng thể mở tệp!", "checkpoint_timeout": "<PERSON><PERSON> hết thời gian khi cố gắng khôi phục điểm kiểm tra.", "checkpoint_failed": "<PERSON><PERSON><PERSON><PERSON> thể khôi phục điểm kiểm tra.", "git_not_installed": "<PERSON><PERSON><PERSON> cầu Git cho tính năng điểm kiểm tra. <PERSON>ui lòng cài đặt Git để bật điểm kiểm tra.", "no_workspace": "<PERSON><PERSON> lòng mở thư mục dự án trước", "update_support_prompt": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật lời nhắc hỗ trợ", "reset_support_prompt": "<PERSON><PERSON><PERSON><PERSON> thể đặt lại lời nhắc hỗ trợ", "enhance_prompt": "<PERSON><PERSON><PERSON><PERSON> thể nâng cao lời nhắc", "get_system_prompt": "<PERSON><PERSON><PERSON><PERSON> thể lấy lời nh<PERSON>c hệ thống", "search_commits": "<PERSON><PERSON><PERSON><PERSON> thể tìm kiếm c<PERSON>c <PERSON>", "save_api_config": "<PERSON><PERSON><PERSON><PERSON> thể lưu cấu hình <PERSON>", "create_api_config": "<PERSON><PERSON><PERSON><PERSON> thể tạo cấu hình <PERSON>", "rename_api_config": "<PERSON><PERSON><PERSON><PERSON> thể đổi tên cấu hình <PERSON>", "load_api_config": "<PERSON><PERSON><PERSON><PERSON> thể tải cấu hình <PERSON>", "delete_api_config": "<PERSON><PERSON><PERSON><PERSON> thể xóa cấu hình <PERSON>", "list_api_config": "<PERSON><PERSON><PERSON><PERSON> thể lấy danh sách cấu hình <PERSON>", "update_server_timeout": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật thời gian chờ máy chủ", "hmr_not_running": "<PERSON>áy chủ phát triển cục bộ không chạy, HMR sẽ không hoạt động. <PERSON><PERSON> lòng chạy 'npm run dev' trước khi khởi chạy tiện ích mở rộng để bật HMR.", "retrieve_current_mode": "Lỗi không thể truy xuất chế độ hiện tại từ trạng thái.", "failed_delete_repo": "<PERSON><PERSON><PERSON><PERSON> thể xóa kho lưu trữ hoặc nhánh liên quan: {{error}}", "failed_remove_directory": "<PERSON><PERSON><PERSON><PERSON> thể xóa thư mục nhi<PERSON>m vụ: {{error}}", "custom_storage_path_unusable": "Đường dẫn lưu trữ tùy chỉnh \"{{path}}\" không thể sử dụng được, sẽ sử dụng đường dẫn mặc định", "cannot_access_path": "<PERSON><PERSON><PERSON><PERSON> thể truy cập đường dẫn {{path}}: {{error}}", "settings_import_failed": "<PERSON><PERSON><PERSON><PERSON> cài đặt thất bại: {{error}}.", "mistake_limit_guidance": "Điều này có thể cho thấy sự thất bại trong quá trình suy nghĩ của mô hình hoặc không thể sử dụng công cụ đúng cách, có thể được giảm thiểu bằng hướng dẫn của người dùng (ví dụ: \"H<PERSON>y thử chia nhỏ nhiệm vụ thành các bước nhỏ hơn\").", "violated_organization_allowlist": "<PERSON>h<PERSON>ng thể chạy tác vụ: hồ sơ hiện tại không tương thích với cài đặt của tổ chức của bạn", "condense_failed": "<PERSON><PERSON><PERSON><PERSON> thể nén ngữ cảnh", "condense_not_enough_messages": "<PERSON>hông đủ tin nhắn để nén ngữ cảnh", "condensed_recently": "Ngữ cảnh đã được nén gần đây; bỏ qua lần thử này", "condense_handler_invalid": "<PERSON><PERSON><PERSON><PERSON> xử lý API để nén ngữ cảnh không hợp lệ", "condense_context_grew": "<PERSON><PERSON><PERSON> thước ngữ cảnh tăng lên trong quá trình nén; bỏ qua lần thử này", "url_timeout": "Trang web mất quá nhiều thời gian để tải (timeout). Điều này có thể do kết nối chậm, trang web nặng hoặc tạm thời không khả dụng. Bạn có thể thử lại sau hoặc kiểm tra xem URL có đúng không.", "url_not_found": "<PERSON>hông thể tìm thấy địa chỉ trang web. V<PERSON> lòng kiểm tra URL có đúng không và thử lại.", "no_internet": "<PERSON><PERSON><PERSON><PERSON> có kết nối internet. Vui lòng kiểm tra kết nối mạng và thử lại.", "url_forbidden": "T<PERSON>y cập vào trang web này bị cấm. Trang có thể chặn truy cập tự động hoặc yêu cầu xác thực.", "url_page_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy trang. <PERSON>ui lòng kiểm tra URL có đúng không.", "url_fetch_failed": "Lỗi lấy nội dung URL: {{error}}", "url_fetch_error_with_url": "Lỗi lấy nội dung cho {{url}}: {{error}}", "command_timeout": "<PERSON>h<PERSON><PERSON> thi lệnh đã hết thời gian chờ sau {{seconds}} gi<PERSON>y", "share_task_failed": "<PERSON><PERSON><PERSON><PERSON> thể chia sẻ nhiệm vụ", "share_no_active_task": "<PERSON><PERSON><PERSON><PERSON> có nhiệm vụ hoạt động để chia sẻ", "share_auth_required": "<PERSON><PERSON><PERSON> x<PERSON>c thực. <PERSON><PERSON> lòng đăng nhập để chia sẻ nhiệm vụ.", "share_not_enabled": "<PERSON><PERSON> sẻ nhiệm vụ không đượ<PERSON> bật cho tổ chức này.", "share_task_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nhiệm vụ hoặc truy cập bị từ chối.", "mode_import_failed": "<PERSON><PERSON><PERSON><PERSON> chế độ thất bại: {{error}}", "delete_rules_folder_failed": "<PERSON><PERSON><PERSON><PERSON> thể xóa thư mục quy tắc: {{rulesFolderPath}}. Lỗi: {{error}}", "command_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy lệnh '{{name}}'", "open_command_file": "<PERSON><PERSON><PERSON><PERSON> thể mở tệp lệnh", "delete_command": "<PERSON><PERSON><PERSON><PERSON> thể xóa lệnh", "no_workspace_for_project_command": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thư mục workspace cho lệnh dự án", "command_already_exists": "<PERSON><PERSON><PERSON> \"{{commandName}}\" đã tồn tại", "create_command_failed": "<PERSON><PERSON><PERSON><PERSON> thể tạo lệnh", "command_template_content": "---\ndescription: \"<PERSON><PERSON> tả ngắn gọn về chức năng của lệnh này\"\n---\n\n<PERSON><PERSON><PERSON> là một lệnh slash mới. Chỉnh sửa tệp này để tùy chỉnh hành vi của lệnh.", "claudeCode": {"processExited": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> Code thoát với mã {{exitCode}}.", "errorOutput": "<PERSON><PERSON>u ra lỗi: {{output}}", "processExitedWithError": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> Code thoát với mã {{exitCode}}. Đầu ra lỗi: {{output}}", "stoppedWithReason": "<PERSON> Code dừng lại vì lý do: {{reason}}", "apiKeyModelPlanMismatch": "API keys and subscription plans allow different models. Make sure the selected model is included in your plan.", "notFound": "Claude Code executable '{{claudePath}}' not found.\n\nPlease install Claude Code CLI:\n1. Visit {{installationUrl}} to download Claude Code\n2. Follow the installation instructions for your operating system\n3. Ensure the 'claude' command is available in your PATH\n4. Alternatively, configure a custom path in Roo settings under 'Claude Code Path'\n\nOriginal error: {{originalError}}"}, "gemini": {"generate_stream": "Lỗi luồng ngữ cảnh tạo Gemini: {{error}}", "generate_complete_prompt": "Lỗi hoàn thành <PERSON>: {{error}}", "sources": "Nguồn:"}, "cerebras": {"authenticationFailed": "Xác thực API Cerebras thất bại. <PERSON><PERSON> lòng kiểm tra khóa API của bạn có hợp lệ và chưa hết hạn.", "accessForbidden": "Truy cập API Cerebras bị từ chối. Khóa API của bạn có thể không có quyền truy cập vào mô hình hoặc tính năng được yêu cầu.", "rateLimitExceeded": "<PERSON><PERSON><PERSON><PERSON> quá giới hạn tốc độ API Cerebras. <PERSON><PERSON> lòng chờ trước khi thực hiện yêu cầu kh<PERSON>c.", "serverError": "Lỗi máy chủ API Cerebras ({{status}}). <PERSON><PERSON> lòng thử lại sau.", "genericError": "Lỗi API Cerebras ({{status}}): {{message}}", "noResponseBody": "Lỗi API Cerebras: <PERSON><PERSON><PERSON><PERSON> có nội dung phản hồi", "completionError": "Lỗi hoàn thành <PERSON>: {{error}}"}}, "warnings": {"no_terminal_content": "<PERSON><PERSON><PERSON><PERSON> có nội dung terminal đ<PERSON><PERSON><PERSON> chọn", "missing_task_files": "<PERSON><PERSON><PERSON> tệp của nhiệm vụ này bị thiếu. Bạn có muốn xóa nó khỏi danh sách nhiệm vụ không?", "auto_import_failed": "<PERSON><PERSON><PERSON><PERSON> thể tự động nhập cài đặt RooCode: {{error}}"}, "info": {"no_changes": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thay đổi nào.", "clipboard_copy": "<PERSON><PERSON><PERSON> n<PERSON><PERSON><PERSON> hệ thống đã đư<PERSON>c sao chép thành công vào clipboard", "history_cleanup": "<PERSON><PERSON> dọn dẹp {{count}} nhiệm vụ có tệp bị thiếu khỏi lịch sử.", "custom_storage_path_set": "<PERSON><PERSON> thiết lập đường dẫn lưu trữ tùy chỉnh: {{path}}", "default_storage_path": "Đã quay lại sử dụng đường dẫn lưu trữ mặc định", "settings_imported": "<PERSON>ài đặt đã được nhập thành công.", "auto_import_success": "Cài đặt RooCode đã được tự động nhập từ {{filename}}", "share_link_copied": "<PERSON><PERSON><PERSON> kết chia sẻ đã được sao chép vào clipboard", "image_copied_to_clipboard": "URI dữ liệu hình ảnh đã đư<PERSON>c sao chép vào clipboard", "image_saved": "<PERSON><PERSON><PERSON>nh đã đư<PERSON><PERSON> lưu vào {{path}}", "organization_share_link_copied": "<PERSON><PERSON><PERSON> kết chia sẻ tổ chức đã được sao chép vào clipboard!", "public_share_link_copied": "<PERSON><PERSON><PERSON> kết chia sẻ công khai đã được sao chép vào clipboard!", "mode_exported": "Chế độ '{{mode}}' đã được xuất thành công", "mode_imported": "<PERSON>ế độ đã đư<PERSON><PERSON> nhập thành công"}, "answers": {"yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "remove": "Xóa", "keep": "Giữ"}, "buttons": {"save": "<PERSON><PERSON><PERSON>", "edit": "Chỉnh sửa", "learn_more": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm"}, "tasks": {"canceled": "Lỗi nhiệm vụ: <PERSON><PERSON> đã bị dừng và hủy bởi người dùng.", "deleted": "Nhiệm vụ thất bại: <PERSON><PERSON> đã bị dừng và xóa bởi người dùng.", "incomplete": "<PERSON><PERSON><PERSON><PERSON> vụ #{{taskNumber}} (<PERSON><PERSON><PERSON> ho<PERSON>n thành)", "no_messages": "<PERSON>hiệm vụ #{{taskNumber}} (<PERSON><PERSON><PERSON><PERSON> có tin nh<PERSON>)"}, "storage": {"prompt_custom_path": "<PERSON><PERSON><PERSON><PERSON> đường dẫn lưu trữ tùy chỉnh cho lịch sử hội thoại, để trống để sử dụng vị trí mặc định", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "<PERSON><PERSON> lòng nhập đường dẫn tuyệt đối (ví dụ: D:\\RooCodeStorage hoặc /home/<USER>/storage)", "enter_valid_path": "<PERSON><PERSON> lòng nhập đường dẫn hợp lệ"}, "input": {"task_prompt": "Bạn muốn <PERSON>oo làm gì?", "task_placeholder": "<PERSON><PERSON><PERSON><PERSON> nhi<PERSON> vụ của bạn ở đây"}, "settings": {"providers": {"groqApiKey": "Khóa API Groq", "getGroqApiKey": "Lấy khóa API Groq", "claudeCode": {"pathLabel": "Đường dẫn Claude Code", "description": "Đường dẫn tùy chọn đến CLI Claude Code của bạn. Mặc định là 'claude' nếu không được đặt.", "placeholder": "Mặc định: claude"}}}, "customModes": {"errors": {"yamlParseError": "YAML không hợp lệ trong tệp .roomodes tại dòng {{line}}. Vui lòng kiểm tra:\n• Th<PERSON><PERSON> lề đúng (dùng dấu cách, không dùng tab)\n• Dấu ngoặc kép và ngoặc đơn khớp nhau\n• Cú pháp YAML hợp lệ", "schemaValidationError": "<PERSON><PERSON><PERSON> dạng chế độ tùy chỉnh không hợp lệ trong .roomodes:\n{{issues}}", "invalidFormat": "Định dạng chế độ tùy chỉnh không hợp lệ. <PERSON><PERSON> lòng đảm bảo cài đặt của bạn tuân theo định dạng YAML đúng.", "updateFailed": "<PERSON><PERSON><PERSON> nhật chế độ tùy chỉnh thất bại: {{error}}", "deleteFailed": "<PERSON><PERSON><PERSON> chế độ tùy chỉnh thất bại: {{error}}", "resetFailed": "Đặt lại chế độ tùy chỉnh thất bại: {{error}}", "modeNotFound": "Lỗi ghi: <PERSON><PERSON><PERSON><PERSON> tìm thấy chế độ", "noWorkspaceForProject": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thư mục workspace cho chế độ dành riêng cho dự án", "rulesCleanupFailed": "<PERSON><PERSON> xóa chế độ thành công, nh<PERSON><PERSON> không thể xóa thư mục quy tắc tại {{rulesFolderPath}}. <PERSON><PERSON><PERSON> có thể cần xóa thủ công."}, "scope": {"project": "d<PERSON>n", "global": "<PERSON><PERSON><PERSON> c<PERSON>u"}}, "marketplace": {"mode": {"rulesCleanupFailed": "<PERSON><PERSON> xóa chế độ thành công, nh<PERSON><PERSON> không thể xóa thư mục quy tắc tại {{rulesFolderPath}}. <PERSON><PERSON><PERSON> có thể cần xóa thủ công."}}, "mdm": {"errors": {"cloud_auth_required": "Tổ chức của bạn yêu cầu xác thực <PERSON>oo <PERSON> Cloud. <PERSON><PERSON> lòng đăng nhập để tiếp tục.", "organization_mismatch": "Bạn phải được xác thực bằng tài khoản Roo Code Cloud của tổ chức.", "verification_failed": "<PERSON><PERSON><PERSON><PERSON> thể xác minh xác thực tổ chức."}}, "prompts": {"deleteMode": {"title": "<PERSON><PERSON><PERSON> chế độ tùy chỉnh", "description": "Bạn có chắc chắn muốn xóa chế độ {{scope}} này không? Thao tác này cũng θα xóa thư mục quy tắc liên quan tại {{rulesFolderPath}}", "translations": {"title": "<PERSON><PERSON><PERSON> ch<PERSON> độ", "description": "Bạn có chắc chắn muốn xóa chế độ này không?", "deleteMessage": "Chỉ chế độ này", "rulesFolderMessage": "<PERSON><PERSON><PERSON> mục quy tắc cũng sẽ bị xóa nếu tồn tại.", "deleteConfirmation": "Chắc chắn xóa chế độ này?"}, "descriptionNoRules": "Bạn có chắc chắn muốn xóa chế độ tùy chỉnh này không?", "confirm": "Xóa"}}, "commands": {"preventCompletionWithOpenTodos": {"description": "Ngăn chặn hoàn thành nhiệm vụ khi có các todo chưa hoàn thành trong danh sách todo"}}}