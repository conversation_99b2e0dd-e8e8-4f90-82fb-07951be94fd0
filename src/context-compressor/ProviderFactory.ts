import { LLMProvider } from './ContextCompressor';
import { OpenAIProvider } from './providers/OpenAIProvider';
import { AnthropicProvider } from './providers/AnthropicProvider';
import { GeminiProvider } from './providers/GeminiProvider';

/**
 * 提供商类型枚举
 */
export enum ProviderType {
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic',
  GEMINI = 'gemini',
  CUSTOM = 'custom'
}

/**
 * 提供商配置接口
 */
export interface ProviderConfig {
  type: ProviderType;
  apiKey: string;
  modelId?: string;
  baseURL?: string;
  customProvider?: LLMProvider;
}

/**
 * 提供商工厂类
 */
export class ProviderFactory {
  private static providers: Map<string, LLMProvider> = new Map();

  /**
   * 创建LLM提供商实例
   */
  static createProvider(config: ProviderConfig): LLMProvider {
    const cacheKey = `${config.type}-${config.modelId || 'default'}-${config.baseURL || 'default'}`;
    
    // 检查缓存
    if (this.providers.has(cacheKey)) {
      return this.providers.get(cacheKey)!;
    }

    let provider: LLMProvider;

    switch (config.type) {
      case ProviderType.OPENAI:
        provider = new OpenAIProvider(
          config.apiKey,
          config.modelId as any,
          config.baseURL
        );
        break;

      case ProviderType.ANTHROPIC:
        provider = new AnthropicProvider(
          config.apiKey,
          config.modelId as any,
          config.baseURL
        );
        break;

      case ProviderType.GEMINI:
        provider = new GeminiProvider(
          config.apiKey,
          config.modelId as any
        );
        break;

      case ProviderType.CUSTOM:
        if (!config.customProvider) {
          throw new Error('Custom provider instance is required for CUSTOM type');
        }
        provider = config.customProvider;
        break;

      default:
        throw new Error(`Unsupported provider type: ${config.type}`);
    }

    // 缓存提供商实例
    this.providers.set(cacheKey, provider);
    return provider;
  }

  /**
   * 获取所有可用的模型
   */
  static getAvailableModels(): Record<ProviderType, string[]> {
    return {
      [ProviderType.OPENAI]: OpenAIProvider.getAvailableModels(),
      [ProviderType.ANTHROPIC]: AnthropicProvider.getAvailableModels(),
      [ProviderType.GEMINI]: GeminiProvider.getAvailableModels(),
      [ProviderType.CUSTOM]: []
    };
  }

  /**
   * 清除提供商缓存
   */
  static clearCache(): void {
    this.providers.clear();
  }

  /**
   * 测试提供商连接
   */
  static async testProvider(config: ProviderConfig): Promise<boolean> {
    try {
      const provider = this.createProvider(config);
      
      // 如果提供商有测试方法，使用它
      if ('testConnection' in provider && typeof provider.testConnection === 'function') {
        return await (provider as any).testConnection();
      }
      
      // 否则尝试基本的token估算测试
      const testMessages = [{ role: 'user' as const, content: 'test' }];
      const tokens = await provider.estimateTokens(testMessages);
      return tokens > 0;
    } catch (error) {
      console.error('Provider test failed:', error);
      return false;
    }
  }

  /**
   * 获取推荐的提供商配置
   */
  static getRecommendedConfigs(): Record<string, ProviderConfig> {
    return {
      'cost-effective': {
        type: ProviderType.GEMINI,
        apiKey: '', // 需要用户填入
        modelId: 'gemini-1.5-flash-8b'
      },
      'high-quality': {
        type: ProviderType.ANTHROPIC,
        apiKey: '', // 需要用户填入
        modelId: 'claude-3-5-sonnet-20241022'
      },
      'balanced': {
        type: ProviderType.GEMINI,
        apiKey: '', // 需要用户填入
        modelId: 'gemini-1.5-flash'
      },
      'large-context': {
        type: ProviderType.GEMINI,
        apiKey: '', // 需要用户填入
        modelId: 'gemini-1.5-pro'
      },
      'ultra-large-context': {
        type: ProviderType.GEMINI,
        apiKey: '', // 需要用户填入
        modelId: 'gemini-1.5-pro' // 2M tokens context
      }
    };
  }
}
