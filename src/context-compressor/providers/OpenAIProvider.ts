import OpenAI from 'openai';
import { LLMProvider, Message } from '../ContextCompressor';

/**
 * OpenAI GPT 模型配置
 */
interface OpenAIModelConfig {
  modelId: string;
  contextWindow: number;
  maxOutputTokens: number;
  inputTokenCost: number;  // 每1K tokens的输入成本
  outputTokenCost: number; // 每1K tokens的输出成本
}

/**
 * 预定义的OpenAI模型配置
 */
const OPENAI_MODELS: Record<string, OpenAIModelConfig> = {
  'gpt-4o': {
    modelId: 'gpt-4o',
    contextWindow: 128000,
    maxOutputTokens: 4096,
    inputTokenCost: 0.005,
    outputTokenCost: 0.015
  },
  'gpt-4o-mini': {
    modelId: 'gpt-4o-mini',
    contextWindow: 128000,
    maxOutputTokens: 16384,
    inputTokenCost: 0.00015,
    outputTokenCost: 0.0006
  },
  'gpt-4-turbo': {
    modelId: 'gpt-4-turbo',
    contextWindow: 128000,
    maxOutputTokens: 4096,
    inputTokenCost: 0.01,
    outputTokenCost: 0.03
  },
  'gpt-3.5-turbo': {
    modelId: 'gpt-3.5-turbo',
    contextWindow: 16385,
    maxOutputTokens: 4096,
    inputTokenCost: 0.0015,
    outputTokenCost: 0.002
  }
};

/**
 * OpenAI提供商实现
 */
export class OpenAIProvider implements LLMProvider {
  public readonly name = 'OpenAI';
  private client: OpenAI;
  private modelConfig: OpenAIModelConfig;

  constructor(
    apiKey: string,
    modelId: keyof typeof OPENAI_MODELS = 'gpt-4o-mini',
    baseURL?: string
  ) {
    this.client = new OpenAI({
      apiKey,
      baseURL
    });
    
    this.modelConfig = OPENAI_MODELS[modelId];
    if (!this.modelConfig) {
      throw new Error(`Unsupported OpenAI model: ${modelId}`);
    }
  }

  /**
   * 估算消息的token数量
   */
  async estimateTokens(messages: Message[]): Promise<number> {
    try {
      // 转换为OpenAI格式
      const openaiMessages = this.convertToOpenAIFormat(messages);
      
      // 使用简单的字符数估算（更精确的方法需要tiktoken库）
      let totalTokens = 0;
      
      for (const message of openaiMessages) {
        // 基础消息开销
        totalTokens += 4; // 每条消息的固定开销
        
        if (typeof message.content === 'string') {
          // 简单估算：平均4个字符 = 1个token
          totalTokens += Math.ceil(message.content.length / 4);
        } else if (Array.isArray(message.content)) {
          for (const content of message.content) {
            if (content.type === 'text' && content.text) {
              totalTokens += Math.ceil(content.text.length / 4);
            } else if (content.type === 'image_url') {
              // 图像token估算（基于OpenAI的定价）
              totalTokens += 765; // 高分辨率图像的平均token数
            }
          }
        }
      }
      
      return totalTokens;
    } catch (error) {
      console.warn('Token estimation failed, using fallback method:', error);
      // 回退到简单的字符数估算
      const totalChars = messages.reduce((sum, msg) => {
        const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
        return sum + content.length;
      }, 0);
      return Math.ceil(totalChars / 4);
    }
  }

  /**
   * 生成对话摘要
   */
  async generateSummary(messages: Message[], prompt: string): Promise<string> {
    try {
      const openaiMessages = this.convertToOpenAIFormat(messages);
      
      const response = await this.client.chat.completions.create({
        model: this.modelConfig.modelId,
        messages: [
          { role: 'system', content: prompt },
          ...openaiMessages,
          { role: 'user', content: 'Please provide a comprehensive summary of the conversation above.' }
        ],
        max_tokens: Math.min(2000, this.modelConfig.maxOutputTokens),
        temperature: 0.1, // 低温度确保一致性
        stream: false
      });

      const summary = response.choices[0]?.message?.content?.trim();
      if (!summary) {
        throw new Error('Empty summary generated');
      }

      return summary;
    } catch (error) {
      throw new Error(`OpenAI summary generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 获取上下文窗口大小
   */
  getContextWindow(): number {
    return this.modelConfig.contextWindow;
  }

  /**
   * 获取最大输出token数
   */
  getMaxOutputTokens(): number {
    return this.modelConfig.maxOutputTokens;
  }

  /**
   * 获取模型配置信息
   */
  getModelConfig(): OpenAIModelConfig {
    return { ...this.modelConfig };
  }

  /**
   * 计算API调用成本
   */
  calculateCost(inputTokens: number, outputTokens: number): number {
    const inputCost = (inputTokens / 1000) * this.modelConfig.inputTokenCost;
    const outputCost = (outputTokens / 1000) * this.modelConfig.outputTokenCost;
    return inputCost + outputCost;
  }

  /**
   * 转换消息格式为OpenAI格式
   */
  private convertToOpenAIFormat(messages: Message[]): OpenAI.Chat.Completions.ChatCompletionMessageParam[] {
    return messages.map(msg => {
      if (msg.role === 'system') {
        return {
          role: 'system',
          content: typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content)
        };
      } else if (msg.role === 'user') {
        return {
          role: 'user',
          content: this.convertContent(msg.content)
        };
      } else {
        return {
          role: 'assistant',
          content: typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content)
        };
      }
    });
  }

  /**
   * 转换消息内容格式
   */
  private convertContent(content: Message['content']): string | OpenAI.Chat.Completions.ChatCompletionContentPart[] {
    if (typeof content === 'string') {
      return content;
    }

    if (Array.isArray(content)) {
      return content.map(item => {
        if (item.type === 'text') {
          return {
            type: 'text',
            text: item.text || ''
          };
        } else if (item.type === 'image' || item.type === 'image_url') {
          return {
            type: 'image_url',
            image_url: {
              url: item.url || item.data || '',
              detail: item.detail || 'auto'
            }
          };
        } else {
          // 其他类型转换为文本
          return {
            type: 'text',
            text: JSON.stringify(item)
          };
        }
      });
    }

    return JSON.stringify(content);
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.client.chat.completions.create({
        model: this.modelConfig.modelId,
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 5
      });
      
      return !!response.choices[0]?.message?.content;
    } catch (error) {
      console.error('OpenAI connection test failed:', error);
      return false;
    }
  }

  /**
   * 获取可用模型列表
   */
  static getAvailableModels(): string[] {
    return Object.keys(OPENAI_MODELS);
  }

  /**
   * 创建自定义模型配置
   */
  static createCustomModel(config: OpenAIModelConfig): void {
    OPENAI_MODELS[config.modelId] = config;
  }
}
