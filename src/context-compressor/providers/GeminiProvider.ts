import { GoogleGenerativeAI, GenerativeModel, Content, Part } from '@google/generative-ai';
import { LLMProvider, Message } from '../ContextCompressor';

/**
 * Google Gemini 模型配置
 */
interface GeminiModelConfig {
  modelId: string;
  contextWindow: number;
  maxOutputTokens: number;
  inputTokenCost: number;  // 每1K tokens的输入成本
  outputTokenCost: number; // 每1K tokens的输出成本
}

/**
 * 预定义的Gemini模型配置
 */
const GEMINI_MODELS: Record<string, GeminiModelConfig> = {
  'gemini-1.5-pro': {
    modelId: 'gemini-1.5-pro',
    contextWindow: 2000000, // 2M tokens
    maxOutputTokens: 8192,
    inputTokenCost: 0.00125,
    outputTokenCost: 0.005
  },
  'gemini-1.5-flash': {
    modelId: 'gemini-1.5-flash',
    contextWindow: 1000000, // 1M tokens
    maxOutputTokens: 8192,
    inputTokenCost: 0.000075,
    outputTokenCost: 0.0003
  },
  'gemini-1.5-flash-8b': {
    modelId: 'gemini-1.5-flash-8b',
    contextWindow: 1000000, // 1M tokens
    maxOutputTokens: 8192,
    inputTokenCost: 0.0000375,
    outputTokenCost: 0.00015
  },
  'gemini-pro': {
    modelId: 'gemini-pro',
    contextWindow: 32768,
    maxOutputTokens: 2048,
    inputTokenCost: 0.0005,
    outputTokenCost: 0.0015
  },
  'gemini-pro-vision': {
    modelId: 'gemini-pro-vision',
    contextWindow: 16384,
    maxOutputTokens: 2048,
    inputTokenCost: 0.00025,
    outputTokenCost: 0.0005
  }
};

/**
 * Google Gemini提供商实现
 */
export class GeminiProvider implements LLMProvider {
  public readonly name = 'Gemini';
  private client: GoogleGenerativeAI;
  private model: GenerativeModel;
  private modelConfig: GeminiModelConfig;

  constructor(
    apiKey: string,
    modelId: keyof typeof GEMINI_MODELS = 'gemini-1.5-flash',
    generationConfig?: {
      temperature?: number;
      topP?: number;
      topK?: number;
      maxOutputTokens?: number;
    }
  ) {
    this.client = new GoogleGenerativeAI(apiKey);
    
    this.modelConfig = GEMINI_MODELS[modelId];
    if (!this.modelConfig) {
      throw new Error(`Unsupported Gemini model: ${modelId}`);
    }

    this.model = this.client.getGenerativeModel({
      model: this.modelConfig.modelId,
      generationConfig: {
        temperature: 0.1, // 低温度确保一致性
        topP: 0.8,
        topK: 40,
        maxOutputTokens: Math.min(2000, this.modelConfig.maxOutputTokens),
        ...generationConfig
      }
    });
  }

  /**
   * 估算消息的token数量
   */
  async estimateTokens(messages: Message[]): Promise<number> {
    try {
      const contents = this.convertToGeminiFormat(messages);
      
      // 使用Gemini的countTokens API进行精确计算
      const { totalTokens } = await this.model.countTokens({ contents });
      return totalTokens;
    } catch (error) {
      console.warn('Gemini token counting failed, using fallback method:', error);
      
      // 回退到简单的字符数估算
      const totalChars = messages.reduce((sum, msg) => {
        const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
        return sum + content.length;
      }, 0);
      
      // Gemini的token估算：平均3个字符 = 1个token
      return Math.ceil(totalChars / 3);
    }
  }

  /**
   * 生成对话摘要
   */
  async generateSummary(messages: Message[], prompt: string): Promise<string> {
    try {
      const contents = this.convertToGeminiFormat(messages);
      
      // 创建摘要请求
      const summaryPrompt = `${prompt}\n\nPlease provide a comprehensive summary of the conversation above.`;
      
      const chat = this.model.startChat({
        history: contents.slice(0, -1), // 除了最后一条消息
        generationConfig: {
          temperature: 0.1,
          maxOutputTokens: Math.min(2000, this.modelConfig.maxOutputTokens)
        }
      });

      const result = await chat.sendMessage(summaryPrompt);
      const summary = result.response.text().trim();

      if (!summary) {
        throw new Error('Empty summary generated');
      }

      return summary;
    } catch (error) {
      throw new Error(`Gemini summary generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 获取上下文窗口大小
   */
  getContextWindow(): number {
    return this.modelConfig.contextWindow;
  }

  /**
   * 获取最大输出token数
   */
  getMaxOutputTokens(): number {
    return this.modelConfig.maxOutputTokens;
  }

  /**
   * 获取模型配置信息
   */
  getModelConfig(): GeminiModelConfig {
    return { ...this.modelConfig };
  }

  /**
   * 计算API调用成本
   */
  calculateCost(inputTokens: number, outputTokens: number): number {
    const inputCost = (inputTokens / 1000) * this.modelConfig.inputTokenCost;
    const outputCost = (outputTokens / 1000) * this.modelConfig.outputTokenCost;
    return inputCost + outputCost;
  }

  /**
   * 转换消息格式为Gemini格式
   */
  private convertToGeminiFormat(messages: Message[]): Content[] {
    const contents: Content[] = [];
    let systemInstruction = '';

    for (const msg of messages) {
      if (msg.role === 'system') {
        // Gemini将system消息合并到systemInstruction中
        const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
        systemInstruction += (systemInstruction ? '\n\n' : '') + content;
        continue;
      }

      const role = msg.role === 'assistant' ? 'model' : 'user';
      const parts = this.convertContentToParts(msg.content);

      contents.push({
        role,
        parts
      });
    }

    // 如果有系统指令，将其添加到第一条用户消息中
    if (systemInstruction && contents.length > 0 && contents[0].role === 'user') {
      const systemPart: Part = { text: `System: ${systemInstruction}\n\n` };
      contents[0].parts = [systemPart, ...contents[0].parts];
    }

    return contents;
  }

  /**
   * 转换消息内容为Gemini Parts格式
   */
  private convertContentToParts(content: Message['content']): Part[] {
    if (typeof content === 'string') {
      return [{ text: content }];
    }

    if (Array.isArray(content)) {
      return content.map(item => {
        if (item.type === 'text') {
          return { text: item.text || '' };
        } else if (item.type === 'image') {
          return {
            inlineData: {
              mimeType: item.media_type || 'image/jpeg',
              data: item.data || ''
            }
          };
        } else {
          // 其他类型转换为文本
          return { text: JSON.stringify(item) };
        }
      });
    }

    return [{ text: JSON.stringify(content) }];
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    try {
      const result = await this.model.generateContent('Hello');
      return !!result.response.text();
    } catch (error) {
      console.error('Gemini connection test failed:', error);
      return false;
    }
  }

  /**
   * 获取可用模型列表
   */
  static getAvailableModels(): string[] {
    return Object.keys(GEMINI_MODELS);
  }

  /**
   * 创建自定义模型配置
   */
  static createCustomModel(config: GeminiModelConfig): void {
    GEMINI_MODELS[config.modelId] = config;
  }

  /**
   * 支持流式响应的摘要生成
   */
  async generateSummaryStream(
    messages: Message[], 
    prompt: string,
    onChunk?: (chunk: string) => void
  ): Promise<string> {
    try {
      const contents = this.convertToGeminiFormat(messages);
      const summaryPrompt = `${prompt}\n\nPlease provide a comprehensive summary of the conversation above.`;
      
      const chat = this.model.startChat({
        history: contents.slice(0, -1),
        generationConfig: {
          temperature: 0.1,
          maxOutputTokens: Math.min(2000, this.modelConfig.maxOutputTokens)
        }
      });

      const result = await chat.sendMessageStream(summaryPrompt);
      let summary = '';
      
      for await (const chunk of result.stream) {
        const chunkText = chunk.text();
        summary += chunkText;
        onChunk?.(chunkText);
      }

      return summary.trim();
    } catch (error) {
      throw new Error(`Gemini stream summary generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 获取模型信息
   */
  async getModelInfo(): Promise<any> {
    try {
      // Gemini API目前没有直接的模型信息接口
      // 返回配置信息
      return {
        name: this.modelConfig.modelId,
        contextWindow: this.modelConfig.contextWindow,
        maxOutputTokens: this.modelConfig.maxOutputTokens,
        supportedFeatures: {
          text: true,
          images: this.modelConfig.modelId.includes('vision') || this.modelConfig.modelId.includes('1.5'),
          streaming: true,
          tokenCounting: true
        }
      };
    } catch (error) {
      console.error('Failed to get Gemini model info:', error);
      return null;
    }
  }

  /**
   * 批量处理消息（用于大量消息的高效处理）
   */
  async batchEstimateTokens(messageBatches: Message[][]): Promise<number[]> {
    const results: number[] = [];
    
    for (const messages of messageBatches) {
      try {
        const tokens = await this.estimateTokens(messages);
        results.push(tokens);
      } catch (error) {
        console.warn('Batch token estimation failed for one batch:', error);
        results.push(0);
      }
    }
    
    return results;
  }
}
