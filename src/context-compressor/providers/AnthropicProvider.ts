import Anthropic from '@anthropic-ai/sdk';
import { LLMProvider, Message } from '../ContextCompressor';

/**
 * Anthropic Claude 模型配置
 */
interface AnthropicModelConfig {
  modelId: string;
  contextWindow: number;
  maxOutputTokens: number;
  inputTokenCost: number;  // 每1K tokens的输入成本
  outputTokenCost: number; // 每1K tokens的输出成本
}

/**
 * 预定义的Anthropic模型配置
 */
const ANTHROPIC_MODELS: Record<string, AnthropicModelConfig> = {
  'claude-3-5-sonnet-20241022': {
    modelId: 'claude-3-5-sonnet-20241022',
    contextWindow: 200000,
    maxOutputTokens: 8192,
    inputTokenCost: 0.003,
    outputTokenCost: 0.015
  },
  'claude-3-5-haiku-20241022': {
    modelId: 'claude-3-5-haiku-20241022',
    contextWindow: 200000,
    maxOutputTokens: 8192,
    inputTokenCost: 0.0008,
    outputTokenCost: 0.004
  },
  'claude-3-opus-20240229': {
    modelId: 'claude-3-opus-20240229',
    contextWindow: 200000,
    maxOutputTokens: 4096,
    inputTokenCost: 0.015,
    outputTokenCost: 0.075
  },
  'claude-3-sonnet-20240229': {
    modelId: 'claude-3-sonnet-20240229',
    contextWindow: 200000,
    maxOutputTokens: 4096,
    inputTokenCost: 0.003,
    outputTokenCost: 0.015
  },
  'claude-3-haiku-20240307': {
    modelId: 'claude-3-haiku-20240307',
    contextWindow: 200000,
    maxOutputTokens: 4096,
    inputTokenCost: 0.00025,
    outputTokenCost: 0.00125
  }
};

/**
 * Anthropic提供商实现
 */
export class AnthropicProvider implements LLMProvider {
  public readonly name = 'Anthropic';
  private client: Anthropic;
  private modelConfig: AnthropicModelConfig;

  constructor(
    apiKey: string,
    modelId: keyof typeof ANTHROPIC_MODELS = 'claude-3-5-haiku-20241022',
    baseURL?: string
  ) {
    this.client = new Anthropic({
      apiKey,
      baseURL
    });
    
    this.modelConfig = ANTHROPIC_MODELS[modelId];
    if (!this.modelConfig) {
      throw new Error(`Unsupported Anthropic model: ${modelId}`);
    }
  }

  /**
   * 估算消息的token数量
   */
  async estimateTokens(messages: Message[]): Promise<number> {
    try {
      // 转换为Anthropic格式
      const anthropicMessages = this.convertToAnthropicFormat(messages);
      
      // 使用字符数估算（Anthropic的token计算相对复杂）
      let totalTokens = 0;
      
      for (const message of anthropicMessages) {
        // 基础消息开销
        totalTokens += 3; // 每条消息的固定开销
        
        if (typeof message.content === 'string') {
          // Claude的token估算：平均3.5个字符 = 1个token
          totalTokens += Math.ceil(message.content.length / 3.5);
        } else if (Array.isArray(message.content)) {
          for (const content of message.content) {
            if (content.type === 'text') {
              totalTokens += Math.ceil(content.text.length / 3.5);
            } else if (content.type === 'image') {
              // 图像token估算（基于Anthropic的定价）
              // 图像大小影响token数，这里使用平均值
              totalTokens += 1150; // 平均图像token数
            }
          }
        }
      }
      
      return totalTokens;
    } catch (error) {
      console.warn('Token estimation failed, using fallback method:', error);
      // 回退到简单的字符数估算
      const totalChars = messages.reduce((sum, msg) => {
        const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
        return sum + content.length;
      }, 0);
      return Math.ceil(totalChars / 3.5);
    }
  }

  /**
   * 生成对话摘要
   */
  async generateSummary(messages: Message[], prompt: string): Promise<string> {
    try {
      const anthropicMessages = this.convertToAnthropicFormat(messages);
      
      // 添加摘要请求消息
      const summaryMessages = [
        ...anthropicMessages,
        {
          role: 'user' as const,
          content: 'Please provide a comprehensive summary of the conversation above following the instructions in the system prompt.'
        }
      ];

      const response = await this.client.messages.create({
        model: this.modelConfig.modelId,
        system: prompt,
        messages: summaryMessages,
        max_tokens: Math.min(2000, this.modelConfig.maxOutputTokens),
        temperature: 0.1, // 低温度确保一致性
        stream: false
      });

      const summary = response.content
        .filter(content => content.type === 'text')
        .map(content => content.text)
        .join('\n')
        .trim();

      if (!summary) {
        throw new Error('Empty summary generated');
      }

      return summary;
    } catch (error) {
      throw new Error(`Anthropic summary generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 获取上下文窗口大小
   */
  getContextWindow(): number {
    return this.modelConfig.contextWindow;
  }

  /**
   * 获取最大输出token数
   */
  getMaxOutputTokens(): number {
    return this.modelConfig.maxOutputTokens;
  }

  /**
   * 获取模型配置信息
   */
  getModelConfig(): AnthropicModelConfig {
    return { ...this.modelConfig };
  }

  /**
   * 计算API调用成本
   */
  calculateCost(inputTokens: number, outputTokens: number): number {
    const inputCost = (inputTokens / 1000) * this.modelConfig.inputTokenCost;
    const outputCost = (outputTokens / 1000) * this.modelConfig.outputTokenCost;
    return inputCost + outputCost;
  }

  /**
   * 转换消息格式为Anthropic格式
   */
  private convertToAnthropicFormat(messages: Message[]): Anthropic.Messages.MessageParam[] {
    const result: Anthropic.Messages.MessageParam[] = [];
    
    for (const msg of messages) {
      // Anthropic不支持system role在messages中，需要单独处理
      if (msg.role === 'system') {
        continue; // system消息应该通过system参数传递
      }
      
      if (msg.role === 'user' || msg.role === 'assistant') {
        result.push({
          role: msg.role,
          content: this.convertContent(msg.content)
        });
      }
    }
    
    return result;
  }

  /**
   * 转换消息内容格式
   */
  private convertContent(content: Message['content']): string | Anthropic.Messages.ContentBlock[] {
    if (typeof content === 'string') {
      return content;
    }

    if (Array.isArray(content)) {
      return content.map(item => {
        if (item.type === 'text') {
          return {
            type: 'text',
            text: item.text || ''
          };
        } else if (item.type === 'image') {
          return {
            type: 'image',
            source: {
              type: 'base64',
              media_type: item.media_type || 'image/jpeg',
              data: item.data || ''
            }
          };
        } else {
          // 其他类型转换为文本
          return {
            type: 'text',
            text: JSON.stringify(item)
          };
        }
      });
    }

    return JSON.stringify(content);
  }

  /**
   * 提取系统消息
   */
  extractSystemMessage(messages: Message[]): string {
    const systemMessages = messages.filter(msg => msg.role === 'system');
    return systemMessages.map(msg => 
      typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content)
    ).join('\n\n');
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.client.messages.create({
        model: this.modelConfig.modelId,
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 5
      });
      
      return response.content.length > 0;
    } catch (error) {
      console.error('Anthropic connection test failed:', error);
      return false;
    }
  }

  /**
   * 获取可用模型列表
   */
  static getAvailableModels(): string[] {
    return Object.keys(ANTHROPIC_MODELS);
  }

  /**
   * 创建自定义模型配置
   */
  static createCustomModel(config: AnthropicModelConfig): void {
    ANTHROPIC_MODELS[config.modelId] = config;
  }

  /**
   * 支持流式响应的摘要生成
   */
  async generateSummaryStream(
    messages: Message[], 
    prompt: string,
    onChunk?: (chunk: string) => void
  ): Promise<string> {
    try {
      const anthropicMessages = this.convertToAnthropicFormat(messages);
      
      const summaryMessages = [
        ...anthropicMessages,
        {
          role: 'user' as const,
          content: 'Please provide a comprehensive summary of the conversation above following the instructions in the system prompt.'
        }
      ];

      const stream = await this.client.messages.create({
        model: this.modelConfig.modelId,
        system: prompt,
        messages: summaryMessages,
        max_tokens: Math.min(2000, this.modelConfig.maxOutputTokens),
        temperature: 0.1,
        stream: true
      });

      let summary = '';
      
      for await (const chunk of stream) {
        if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
          const text = chunk.delta.text;
          summary += text;
          onChunk?.(text);
        }
      }

      return summary.trim();
    } catch (error) {
      throw new Error(`Anthropic stream summary generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
