import { ContextCompressor, Message } from '../ContextCompressor';
import { ProviderFactory, ProviderType } from '../ProviderFactory';
import { createCompressor, RECOMMENDED_PROVIDERS, PRESET_CONFIGS } from '../index';

/**
 * Gemini基础使用示例
 */
async function geminiBasicExample() {
  console.log('=== Gemini 基础使用示例 ===');
  
  // 方法1: 使用工厂创建
  const provider = ProviderFactory.createProvider({
    type: ProviderType.GEMINI,
    apiKey: process.env.GEMINI_API_KEY || 'your-gemini-api-key',
    modelId: 'gemini-1.5-flash' // 平衡的选择
  });

  const compressor = new ContextCompressor(provider, {
    compressionThreshold: 70,
    keepRecentMessages: 4
  });

  // 方法2: 使用便捷函数创建
  const quickCompressor = createCompressor({
    type: 'gemini',
    apiKey: process.env.GEMINI_API_KEY || 'your-gemini-api-key',
    modelId: 'gemini-1.5-flash-8b' // 最经济的选择
  });

  // 测试消息
  const messages: Message[] = [
    {
      role: 'system',
      content: 'You are a helpful AI assistant specializing in technology and programming.',
      timestamp: Date.now() - 10000
    },
    {
      role: 'user',
      content: 'Can you explain the differences between various machine learning algorithms?',
      timestamp: Date.now() - 9000
    },
    {
      role: 'assistant',
      content: 'Certainly! Machine learning algorithms can be broadly categorized into supervised, unsupervised, and reinforcement learning. Supervised learning includes algorithms like linear regression, decision trees, random forests, and neural networks...',
      timestamp: Date.now() - 8000
    },
    {
      role: 'user',
      content: 'What about deep learning? How does it relate to traditional ML?',
      timestamp: Date.now() - 7000
    },
    {
      role: 'assistant',
      content: 'Deep learning is actually a subset of machine learning that uses artificial neural networks with multiple layers (hence "deep"). While traditional ML algorithms often require manual feature engineering...',
      timestamp: Date.now() - 6000
    },
    {
      role: 'user',
      content: 'Can you give me a practical example of implementing a neural network?',
      timestamp: Date.now() - 5000
    }
  ];

  try {
    console.log('\n--- 使用 Gemini 1.5 Flash 压缩 ---');
    const result1 = await compressor.compress(messages);
    
    console.log(`压缩方法: ${result1.method}`);
    console.log(`原始消息数: ${messages.length}`);
    console.log(`压缩后消息数: ${result1.messages.length}`);
    console.log(`Token节省: ${result1.tokensRemoved}`);
    console.log(`压缩比: ${(result1.compressionRatio * 100).toFixed(1)}%`);
    
    if (result1.summary) {
      console.log('\n生成的摘要:');
      console.log(result1.summary.substring(0, 200) + '...');
    }

    console.log('\n--- 使用 Gemini 1.5 Flash 8B 压缩 ---');
    const result2 = await quickCompressor.compress(messages);
    
    console.log(`压缩方法: ${result2.method}`);
    console.log(`Token节省: ${result2.tokensRemoved}`);
    console.log(`压缩比: ${(result2.compressionRatio * 100).toFixed(1)}%`);

  } catch (error) {
    console.error('Gemini压缩失败:', error);
  }
}

/**
 * Gemini大上下文处理示例
 */
async function geminiLargeContextExample() {
  console.log('\n=== Gemini 大上下文处理示例 ===');
  
  // 使用Gemini 1.5 Pro处理大量消息
  const compressor = createCompressor({
    type: 'gemini',
    apiKey: process.env.GEMINI_API_KEY || 'your-gemini-api-key',
    modelId: 'gemini-1.5-pro' // 2M tokens上下文窗口
  }, {
    ...PRESET_CONFIGS.QUALITY_OPTIMIZED,
    compressionThreshold: 85, // 更保守的压缩，充分利用大上下文
    customSummaryPrompt: `
      Create a comprehensive summary of this technical conversation.
      Focus on:
      1. Key technical concepts and algorithms discussed
      2. Specific implementation details or code examples
      3. Questions asked and answers provided
      4. Any unresolved issues or follow-up topics
      
      Maintain technical accuracy and preserve important details.
    `
  });

  // 生成大量测试消息
  const largeMessageSet: Message[] = [];
  
  // 添加系统消息
  largeMessageSet.push({
    role: 'system',
    content: 'You are an expert software engineer and AI researcher.',
    timestamp: Date.now() - 50000
  });

  // 生成50条对话消息
  for (let i = 0; i < 50; i++) {
    const isUser = i % 2 === 0;
    largeMessageSet.push({
      role: isUser ? 'user' : 'assistant',
      content: isUser 
        ? `User question ${i + 1}: Can you explain more about ${['neural networks', 'transformers', 'attention mechanisms', 'backpropagation', 'gradient descent'][i % 5]}?`
        : `Assistant response ${i + 1}: Here's a detailed explanation of the concept you asked about. ${['Neural networks are computational models inspired by biological neural networks...', 'Transformers are a type of neural network architecture that has revolutionized NLP...', 'Attention mechanisms allow models to focus on relevant parts of the input...', 'Backpropagation is the algorithm used to train neural networks by computing gradients...', 'Gradient descent is an optimization algorithm used to minimize loss functions...'][i % 5]} ${'This is additional context to make the message longer. '.repeat(20)}`,
      timestamp: Date.now() - (50 - i) * 1000
    });
  }

  console.log(`生成了 ${largeMessageSet.length} 条消息用于测试`);

  try {
    const startTime = Date.now();
    const result = await compressor.compress(largeMessageSet);
    const endTime = Date.now();

    console.log(`\n压缩结果:`);
    console.log(`- 处理时间: ${endTime - startTime}ms`);
    console.log(`- 压缩方法: ${result.method}`);
    console.log(`- 原始消息数: ${largeMessageSet.length}`);
    console.log(`- 压缩后消息数: ${result.messages.length}`);
    console.log(`- Token节省: ${result.tokensRemoved}`);
    console.log(`- 压缩比: ${(result.compressionRatio * 100).toFixed(1)}%`);

    if (result.summary) {
      console.log(`\n摘要长度: ${result.summary.length} 字符`);
      console.log('摘要预览:');
      console.log(result.summary.substring(0, 300) + '...');
    }

    // 显示统计信息
    const stats = compressor.getStats();
    console.log('\n统计信息:', stats);

  } catch (error) {
    console.error('大上下文处理失败:', error);
  }
}

/**
 * Gemini多模态支持示例
 */
async function geminiMultimodalExample() {
  console.log('\n=== Gemini 多模态支持示例 ===');
  
  const compressor = createCompressor({
    type: 'gemini',
    apiKey: process.env.GEMINI_API_KEY || 'your-gemini-api-key',
    modelId: 'gemini-1.5-flash' // 支持图像
  });

  // 包含图像的消息
  const multimodalMessages: Message[] = [
    {
      role: 'system',
      content: 'You are a helpful assistant that can analyze images and text.',
      timestamp: Date.now() - 8000
    },
    {
      role: 'user',
      content: [
        { type: 'text', text: 'Can you analyze this diagram?' },
        { 
          type: 'image', 
          media_type: 'image/jpeg',
          data: 'base64-encoded-image-data-here' // 实际使用时需要真实的base64数据
        }
      ],
      timestamp: Date.now() - 7000
    },
    {
      role: 'assistant',
      content: 'I can see this is a flowchart diagram showing a machine learning pipeline. The diagram illustrates the flow from data preprocessing through model training to evaluation...',
      timestamp: Date.now() - 6000
    },
    {
      role: 'user',
      content: 'What are the key components I should focus on?',
      timestamp: Date.now() - 5000
    }
  ];

  try {
    const result = await compressor.compress(multimodalMessages);
    
    console.log('多模态消息压缩结果:');
    console.log(`- 压缩方法: ${result.method}`);
    console.log(`- 原始消息数: ${multimodalMessages.length}`);
    console.log(`- 压缩后消息数: ${result.messages.length}`);
    console.log(`- Token节省: ${result.tokensRemoved}`);

    if (result.summary) {
      console.log('\n摘要:');
      console.log(result.summary);
    }

  } catch (error) {
    console.error('多模态压缩失败:', error);
  }
}

/**
 * Gemini性能对比示例
 */
async function geminiPerformanceComparison() {
  console.log('\n=== Gemini 模型性能对比 ===');
  
  const models = [
    { id: 'gemini-1.5-flash-8b', name: 'Gemini 1.5 Flash 8B (最经济)' },
    { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash (平衡)' },
    { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro (最强)' }
  ];

  const testMessages: Message[] = [
    { role: 'system', content: 'You are a coding assistant.' },
    { role: 'user', content: 'Explain the concept of recursion in programming.' },
    { role: 'assistant', content: 'Recursion is a programming technique where a function calls itself to solve a problem. It consists of two main components: a base case that stops the recursion, and a recursive case that breaks the problem into smaller subproblems...' },
    { role: 'user', content: 'Can you show me an example?' },
    { role: 'assistant', content: 'Here\'s a classic example of recursion - calculating factorial: function factorial(n) { if (n <= 1) return 1; return n * factorial(n - 1); }' },
    { role: 'user', content: 'What are the pros and cons of using recursion?' }
  ];

  for (const model of models) {
    try {
      console.log(`\n--- 测试 ${model.name} ---`);
      
      const compressor = createCompressor({
        type: 'gemini',
        apiKey: process.env.GEMINI_API_KEY || 'your-gemini-api-key',
        modelId: model.id as any
      }, {
        compressionThreshold: 50 // 低阈值确保触发压缩
      });

      const startTime = Date.now();
      const result = await compressor.compress(testMessages);
      const endTime = Date.now();

      console.log(`处理时间: ${endTime - startTime}ms`);
      console.log(`压缩方法: ${result.method}`);
      console.log(`Token节省: ${result.tokensRemoved}`);
      console.log(`压缩比: ${(result.compressionRatio * 100).toFixed(1)}%`);
      
      if (result.summary) {
        console.log(`摘要质量: ${result.summary.length} 字符`);
      }

    } catch (error) {
      console.error(`${model.name} 测试失败:`, error);
    }
  }
}

/**
 * 运行所有Gemini示例
 */
async function runAllGeminiExamples() {
  try {
    await geminiBasicExample();
    await geminiLargeContextExample();
    await geminiMultimodalExample();
    await geminiPerformanceComparison();
  } catch (error) {
    console.error('Gemini示例运行失败:', error);
  }
}

// 如果直接运行此文件，执行所有示例
if (require.main === module) {
  runAllGeminiExamples();
}

export {
  geminiBasicExample,
  geminiLargeContextExample,
  geminiMultimodalExample,
  geminiPerformanceComparison,
  runAllGeminiExamples
};
