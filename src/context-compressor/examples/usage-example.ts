import { ContextCompressor, Message, CompressionConfig } from '../ContextCompressor';
import { ProviderFactory, ProviderType } from '../ProviderFactory';

/**
 * 基础使用示例
 */
async function basicUsageExample() {
  console.log('=== 基础使用示例 ===');
  
  // 创建OpenAI提供商
  const provider = ProviderFactory.createProvider({
    type: ProviderType.OPENAI,
    apiKey: process.env.OPENAI_API_KEY || 'your-api-key',
    modelId: 'gpt-4o-mini'
  });

  // 创建压缩器实例
  const compressor = new ContextCompressor(provider);

  // 模拟对话消息
  const messages: Message[] = [
    {
      role: 'system',
      content: 'You are a helpful coding assistant.',
      timestamp: Date.now() - 10000
    },
    {
      role: 'user',
      content: 'Can you help me implement a binary search algorithm in Python?',
      timestamp: Date.now() - 9000
    },
    {
      role: 'assistant',
      content: 'Certainly! Here\'s a binary search implementation in Python:\n\n```python\ndef binary_search(arr, target):\n    left, right = 0, len(arr) - 1\n    \n    while left <= right:\n        mid = (left + right) // 2\n        if arr[mid] == target:\n            return mid\n        elif arr[mid] < target:\n            left = mid + 1\n        else:\n            right = mid - 1\n    \n    return -1\n```',
      timestamp: Date.now() - 8000
    },
    {
      role: 'user',
      content: 'Great! Can you also show me how to implement quicksort?',
      timestamp: Date.now() - 7000
    },
    {
      role: 'assistant',
      content: 'Here\'s a quicksort implementation:\n\n```python\ndef quicksort(arr):\n    if len(arr) <= 1:\n        return arr\n    \n    pivot = arr[len(arr) // 2]\n    left = [x for x in arr if x < pivot]\n    middle = [x for x in arr if x == pivot]\n    right = [x for x in arr if x > pivot]\n    \n    return quicksort(left) + middle + quicksort(right)\n```',
      timestamp: Date.now() - 6000
    },
    {
      role: 'user',
      content: 'Now I need help with merge sort as well.',
      timestamp: Date.now() - 5000
    }
  ];

  try {
    // 执行压缩
    const result = await compressor.compress(messages);
    
    console.log('压缩结果:');
    console.log(`- 方法: ${result.method}`);
    console.log(`- 原始消息数: ${messages.length}`);
    console.log(`- 压缩后消息数: ${result.messages.length}`);
    console.log(`- Token减少: ${result.tokensRemoved}`);
    console.log(`- 压缩比: ${(result.compressionRatio * 100).toFixed(1)}%`);
    
    if (result.summary) {
      console.log('\n生成的摘要:');
      console.log(result.summary);
    }

    // 获取统计信息
    const stats = compressor.getStats();
    console.log('\n统计信息:', stats);

  } catch (error) {
    console.error('压缩失败:', error);
  }
}

/**
 * 高级配置示例
 */
async function advancedConfigExample() {
  console.log('\n=== 高级配置示例 ===');
  
  // 创建Anthropic提供商用于主要对话
  const mainProvider = ProviderFactory.createProvider({
    type: ProviderType.ANTHROPIC,
    apiKey: process.env.ANTHROPIC_API_KEY || 'your-api-key',
    modelId: 'claude-3-5-sonnet-20241022'
  });

  // 创建更便宜的提供商专门用于摘要
  const summaryProvider = ProviderFactory.createProvider({
    type: ProviderType.ANTHROPIC,
    apiKey: process.env.ANTHROPIC_API_KEY || 'your-api-key',
    modelId: 'claude-3-5-haiku-20241022'
  });

  // 高级配置
  const config: Partial<CompressionConfig> = {
    autoCompress: true,
    compressionThreshold: 70, // 70%时触发压缩
    bufferPercentage: 15, // 15%缓冲区
    keepRecentMessages: 5, // 保留最近5条消息
    preferIntelligentCompression: true,
    fallbackToSlidingWindow: true,
    summaryProvider: summaryProvider, // 使用专门的摘要提供商
    customSummaryPrompt: `
      Create a concise but comprehensive summary of the conversation.
      Focus on:
      1. Key technical concepts discussed
      2. Code examples and algorithms mentioned
      3. Current task or question being addressed
      4. Any specific requirements or constraints mentioned
      
      Keep the summary under 500 words but ensure all critical information is preserved.
    `,
    profileThresholds: {
      'development': 60,  // 开发环境更激进的压缩
      'production': 85,   // 生产环境更保守的压缩
      'testing': 50       // 测试环境最激进的压缩
    }
  };

  const compressor = new ContextCompressor(mainProvider, config);

  // 监听压缩事件
  compressor.on('compressionStarted', (data) => {
    console.log(`开始压缩: ${data.initialTokens} tokens, 方法: ${data.method}`);
  });

  compressor.on('compressionCompleted', (result) => {
    console.log(`压缩完成: 节省了 ${result.tokensRemoved} tokens`);
  });

  compressor.on('compressionFallback', (data) => {
    console.log(`压缩回退: ${data.error}`);
  });

  // 模拟长对话
  const longMessages: Message[] = Array.from({ length: 20 }, (_, i) => ({
    role: i % 2 === 0 ? 'user' : 'assistant',
    content: `This is message ${i + 1}. `.repeat(50), // 长消息
    timestamp: Date.now() - (20 - i) * 1000
  }));

  try {
    // 使用不同的配置文件进行压缩
    const devResult = await compressor.compress(longMessages, 'development');
    console.log('开发环境压缩结果:', {
      method: devResult.method,
      compressionRatio: devResult.compressionRatio,
      tokensRemoved: devResult.tokensRemoved
    });

    const prodResult = await compressor.compress(longMessages, 'production');
    console.log('生产环境压缩结果:', {
      method: prodResult.method,
      compressionRatio: prodResult.compressionRatio,
      tokensRemoved: prodResult.tokensRemoved
    });

  } catch (error) {
    console.error('高级配置示例失败:', error);
  }
}

/**
 * 多提供商比较示例
 */
async function multiProviderComparisonExample() {
  console.log('\n=== 多提供商比较示例 ===');
  
  const testMessages: Message[] = [
    { role: 'system', content: 'You are a helpful assistant.' },
    { role: 'user', content: 'Explain quantum computing in simple terms.' },
    { role: 'assistant', content: 'Quantum computing is a revolutionary approach to computation that leverages the principles of quantum mechanics...' },
    { role: 'user', content: 'How does it differ from classical computing?' },
    { role: 'assistant', content: 'Classical computers use bits that can be either 0 or 1, while quantum computers use quantum bits (qubits)...' }
  ];

  const providers = [
    {
      name: 'OpenAI GPT-4o-mini',
      provider: ProviderFactory.createProvider({
        type: ProviderType.OPENAI,
        apiKey: process.env.OPENAI_API_KEY || 'your-api-key',
        modelId: 'gpt-4o-mini'
      })
    },
    {
      name: 'Anthropic Claude Haiku',
      provider: ProviderFactory.createProvider({
        type: ProviderType.ANTHROPIC,
        apiKey: process.env.ANTHROPIC_API_KEY || 'your-api-key',
        modelId: 'claude-3-5-haiku-20241022'
      })
    }
  ];

  for (const { name, provider } of providers) {
    try {
      console.log(`\n测试提供商: ${name}`);
      
      const compressor = new ContextCompressor(provider, {
        compressionThreshold: 50 // 低阈值确保触发压缩
      });

      const result = await compressor.compress(testMessages);
      
      console.log(`- 上下文窗口: ${provider.getContextWindow()}`);
      console.log(`- 最大输出: ${provider.getMaxOutputTokens()}`);
      console.log(`- 压缩方法: ${result.method}`);
      console.log(`- Token减少: ${result.tokensRemoved}`);
      
      if (result.summary) {
        console.log(`- 摘要长度: ${result.summary.length} 字符`);
      }

    } catch (error) {
      console.error(`${name} 测试失败:`, error);
    }
  }
}

/**
 * 性能测试示例
 */
async function performanceTestExample() {
  console.log('\n=== 性能测试示例 ===');
  
  const provider = ProviderFactory.createProvider({
    type: ProviderType.OPENAI,
    apiKey: process.env.OPENAI_API_KEY || 'your-api-key',
    modelId: 'gpt-4o-mini'
  });

  const compressor = new ContextCompressor(provider);

  // 生成大量测试消息
  const largeMessageSet: Message[] = Array.from({ length: 100 }, (_, i) => ({
    role: i % 2 === 0 ? 'user' : 'assistant',
    content: `Message ${i}: ${'Lorem ipsum dolor sit amet, consectetur adipiscing elit. '.repeat(10)}`,
    timestamp: Date.now() - (100 - i) * 1000
  }));

  console.log(`测试消息数量: ${largeMessageSet.length}`);
  
  const startTime = Date.now();
  
  try {
    const result = await compressor.compress(largeMessageSet);
    const endTime = Date.now();
    
    console.log(`压缩耗时: ${endTime - startTime}ms`);
    console.log(`压缩效果: ${largeMessageSet.length} -> ${result.messages.length} 条消息`);
    console.log(`Token节省: ${result.tokensRemoved}`);
    console.log(`压缩比: ${(result.compressionRatio * 100).toFixed(1)}%`);
    
    const stats = compressor.getStats();
    console.log('最终统计:', stats);

  } catch (error) {
    console.error('性能测试失败:', error);
  }
}

/**
 * 运行所有示例
 */
async function runAllExamples() {
  try {
    await basicUsageExample();
    await advancedConfigExample();
    await multiProviderComparisonExample();
    await performanceTestExample();
  } catch (error) {
    console.error('示例运行失败:', error);
  }
}

// 如果直接运行此文件，执行所有示例
if (require.main === module) {
  runAllExamples();
}

export {
  basicUsageExample,
  advancedConfigExample,
  multiProviderComparisonExample,
  performanceTestExample,
  runAllExamples
};
