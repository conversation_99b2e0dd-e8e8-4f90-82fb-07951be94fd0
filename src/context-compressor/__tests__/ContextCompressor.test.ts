import { ContextCompressor, Message, LLMProvider, CompressionConfig } from '../ContextCompressor';

/**
 * 模拟LLM提供商用于测试
 */
class MockLLMProvider implements LLMProvider {
  name = 'Mock';
  private contextWindow: number;
  private maxOutputTokens: number;
  private shouldFailSummary: boolean;

  constructor(
    contextWindow = 100000,
    maxOutputTokens = 4096,
    shouldFailSummary = false
  ) {
    this.contextWindow = contextWindow;
    this.maxOutputTokens = maxOutputTokens;
    this.shouldFailSummary = shouldFailSummary;
  }

  async estimateTokens(messages: Message[]): Promise<number> {
    // 简单的token估算：每个字符约0.25个token
    return messages.reduce((total, msg) => {
      const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
      return total + Math.ceil(content.length * 0.25);
    }, 0);
  }

  async generateSummary(messages: Message[], prompt: string): Promise<string> {
    if (this.shouldFailSummary) {
      throw new Error('Mock summary generation failed');
    }
    
    return `Mock summary of ${messages.length} messages. Key points: conversation about various topics, technical discussions, and user interactions.`;
  }

  getContextWindow(): number {
    return this.contextWindow;
  }

  getMaxOutputTokens(): number {
    return this.maxOutputTokens;
  }

  setShouldFailSummary(shouldFail: boolean): void {
    this.shouldFailSummary = shouldFail;
  }
}

describe('ContextCompressor', () => {
  let mockProvider: MockLLMProvider;
  let compressor: ContextCompressor;
  let testMessages: Message[];

  beforeEach(() => {
    mockProvider = new MockLLMProvider();
    compressor = new ContextCompressor(mockProvider);
    
    // 创建测试消息
    testMessages = [
      {
        role: 'system',
        content: 'You are a helpful assistant.',
        timestamp: Date.now() - 10000
      },
      {
        role: 'user',
        content: 'Hello, how are you?',
        timestamp: Date.now() - 9000
      },
      {
        role: 'assistant',
        content: 'I\'m doing well, thank you for asking! How can I help you today?',
        timestamp: Date.now() - 8000
      },
      {
        role: 'user',
        content: 'Can you explain quantum computing?',
        timestamp: Date.now() - 7000
      },
      {
        role: 'assistant',
        content: 'Quantum computing is a revolutionary approach to computation that leverages quantum mechanical phenomena...',
        timestamp: Date.now() - 6000
      },
      {
        role: 'user',
        content: 'That\'s interesting. How does it compare to classical computing?',
        timestamp: Date.now() - 5000
      }
    ];
  });

  describe('基础功能', () => {
    test('应该正确初始化', () => {
      expect(compressor).toBeInstanceOf(ContextCompressor);
      expect(compressor.getConfig()).toBeDefined();
      expect(compressor.getStats()).toBeDefined();
    });

    test('应该正确估算token数量', async () => {
      const tokens = await mockProvider.estimateTokens(testMessages);
      expect(tokens).toBeGreaterThan(0);
    });

    test('应该在不需要压缩时返回原始消息', async () => {
      // 设置高阈值，确保不触发压缩
      compressor.updateConfig({ compressionThreshold: 95 });
      
      const result = await compressor.compress(testMessages);
      
      expect(result.method).toBe('none');
      expect(result.messages).toEqual(testMessages);
      expect(result.tokensRemoved).toBe(0);
      expect(result.compressionRatio).toBe(1.0);
    });
  });

  describe('智能压缩', () => {
    test('应该成功执行智能压缩', async () => {
      // 设置低阈值确保触发压缩
      compressor.updateConfig({ 
        compressionThreshold: 10,
        preferIntelligentCompression: true
      });
      
      const result = await compressor.compress(testMessages);
      
      expect(result.method).toBe('intelligent');
      expect(result.summary).toBeDefined();
      expect(result.summary).toContain('Mock summary');
      expect(result.messages.length).toBeLessThan(testMessages.length);
      expect(result.tokensRemoved).toBeGreaterThan(0);
      expect(result.compressionRatio).toBeLessThan(1.0);
    });

    test('应该在智能压缩失败时回退到滑动窗口', async () => {
      // 设置提供商失败
      mockProvider.setShouldFailSummary(true);
      
      compressor.updateConfig({ 
        compressionThreshold: 10,
        preferIntelligentCompression: true,
        fallbackToSlidingWindow: true
      });
      
      const result = await compressor.compress(testMessages);
      
      expect(result.method).toBe('sliding-window');
      expect(result.summary).toBeUndefined();
      expect(result.messages.length).toBeLessThan(testMessages.length);
    });

    test('应该保留配置的最近消息数量', async () => {
      const keepCount = 2;
      compressor.updateConfig({ 
        compressionThreshold: 10,
        keepRecentMessages: keepCount,
        preferIntelligentCompression: true
      });
      
      const result = await compressor.compress(testMessages);
      
      // 应该保留：第一条消息 + 摘要消息 + 最近N条消息
      const expectedMinLength = 1 + 1 + keepCount; // first + summary + recent
      expect(result.messages.length).toBeGreaterThanOrEqual(expectedMinLength);
      
      // 检查最后N条消息是否被保留
      const originalLast = testMessages.slice(-keepCount);
      const resultLast = result.messages.slice(-keepCount);
      expect(resultLast).toEqual(originalLast);
    });
  });

  describe('滑动窗口压缩', () => {
    test('应该正确执行滑动窗口压缩', async () => {
      compressor.updateConfig({ 
        compressionThreshold: 10,
        preferIntelligentCompression: false,
        slidingWindowRemovalRatio: 0.5
      });
      
      const result = await compressor.compress(testMessages);
      
      expect(result.method).toBe('sliding-window');
      expect(result.summary).toBeUndefined();
      expect(result.messages.length).toBeLessThan(testMessages.length);
      expect(result.tokensRemoved).toBeGreaterThan(0);
    });

    test('应该保留第一条和最后几条消息', async () => {
      const keepCount = 2;
      compressor.updateConfig({ 
        compressionThreshold: 10,
        preferIntelligentCompression: false,
        keepRecentMessages: keepCount,
        keepFirstMessage: true
      });
      
      const result = await compressor.compress(testMessages);
      
      // 检查第一条消息被保留
      expect(result.messages[0]).toEqual(testMessages[0]);
      
      // 检查最后N条消息被保留
      const originalLast = testMessages.slice(-keepCount);
      const resultLast = result.messages.slice(-keepCount);
      expect(resultLast).toEqual(originalLast);
    });
  });

  describe('配置管理', () => {
    test('应该正确更新配置', () => {
      const newConfig: Partial<CompressionConfig> = {
        compressionThreshold: 60,
        keepRecentMessages: 5,
        bufferPercentage: 15
      };
      
      compressor.updateConfig(newConfig);
      const config = compressor.getConfig();
      
      expect(config.compressionThreshold).toBe(60);
      expect(config.keepRecentMessages).toBe(5);
      expect(config.bufferPercentage).toBe(15);
    });

    test('应该支持配置文件级别的阈值', async () => {
      compressor.updateConfig({
        compressionThreshold: 80, // 全局阈值
        profileThresholds: {
          'test-profile': 20 // 测试配置文件的低阈值
        }
      });
      
      const result = await compressor.compress(testMessages, 'test-profile');
      
      // 由于使用了低阈值，应该触发压缩
      expect(result.method).not.toBe('none');
    });
  });

  describe('统计功能', () => {
    test('应该正确更新统计信息', async () => {
      compressor.updateConfig({ compressionThreshold: 10 });
      
      const initialStats = compressor.getStats();
      expect(initialStats.totalCompressions).toBe(0);
      
      await compressor.compress(testMessages);
      
      const updatedStats = compressor.getStats();
      expect(updatedStats.totalCompressions).toBe(1);
      expect(updatedStats.totalTokensSaved).toBeGreaterThan(0);
    });

    test('应该正确重置统计信息', async () => {
      compressor.updateConfig({ compressionThreshold: 10 });
      
      // 执行一次压缩
      await compressor.compress(testMessages);
      expect(compressor.getStats().totalCompressions).toBe(1);
      
      // 重置统计
      compressor.resetStats();
      expect(compressor.getStats().totalCompressions).toBe(0);
    });
  });

  describe('事件系统', () => {
    test('应该触发压缩开始事件', async () => {
      const startHandler = jest.fn();
      compressor.on('compressionStarted', startHandler);
      compressor.updateConfig({ compressionThreshold: 10 });
      
      await compressor.compress(testMessages);
      
      expect(startHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          initialTokens: expect.any(Number),
          threshold: expect.any(Number),
          method: expect.any(String)
        })
      );
    });

    test('应该触发压缩完成事件', async () => {
      const completeHandler = jest.fn();
      compressor.on('compressionCompleted', completeHandler);
      compressor.updateConfig({ compressionThreshold: 10 });
      
      await compressor.compress(testMessages);
      
      expect(completeHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          method: expect.any(String),
          tokensRemoved: expect.any(Number),
          compressionRatio: expect.any(Number)
        })
      );
    });

    test('应该触发压缩回退事件', async () => {
      const fallbackHandler = jest.fn();
      compressor.on('compressionFallback', fallbackHandler);
      
      // 设置智能压缩失败
      mockProvider.setShouldFailSummary(true);
      compressor.updateConfig({ 
        compressionThreshold: 10,
        preferIntelligentCompression: true,
        fallbackToSlidingWindow: true
      });
      
      await compressor.compress(testMessages);
      
      expect(fallbackHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          error: expect.any(String)
        })
      );
    });
  });

  describe('边界情况', () => {
    test('应该处理空消息列表', async () => {
      const result = await compressor.compress([]);
      
      expect(result.method).toBe('none');
      expect(result.messages).toEqual([]);
      expect(result.tokensRemoved).toBe(0);
    });

    test('应该处理单条消息', async () => {
      const singleMessage = [testMessages[0]];
      const result = await compressor.compress(singleMessage);
      
      expect(result.method).toBe('none');
      expect(result.messages).toEqual(singleMessage);
    });

    test('应该处理消息不足的情况', async () => {
      // 只有2条消息，但要保留3条最近消息
      const fewMessages = testMessages.slice(0, 2);
      compressor.updateConfig({ 
        compressionThreshold: 10,
        keepRecentMessages: 3
      });
      
      const result = await compressor.compress(fewMessages);
      
      // 应该不进行压缩或者正确处理
      expect(result.messages.length).toBeGreaterThan(0);
    });
  });
});
