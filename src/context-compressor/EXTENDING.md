# 扩展指南：添加新的LLM提供商

本指南将教您如何为上下文压缩器添加新的LLM提供商支持。我们将以添加一个假想的"CustomLLM"为例。

## 🏗️ 实现步骤

### 1. 创建提供商类

首先，在 `src/context-compressor/providers/` 目录下创建新的提供商文件：

```typescript
// src/context-compressor/providers/CustomLLMProvider.ts
import { LLMProvider, Message } from '../ContextCompressor';

/**
 * 自定义LLM模型配置
 */
interface CustomLLMModelConfig {
  modelId: string;
  contextWindow: number;
  maxOutputTokens: number;
  inputTokenCost: number;
  outputTokenCost: number;
}

/**
 * 预定义的模型配置
 */
const CUSTOM_LLM_MODELS: Record<string, CustomLLMModelConfig> = {
  'custom-model-v1': {
    modelId: 'custom-model-v1',
    contextWindow: 128000,
    maxOutputTokens: 4096,
    inputTokenCost: 0.001,
    outputTokenCost: 0.003
  },
  'custom-model-v2': {
    modelId: 'custom-model-v2',
    contextWindow: 200000,
    maxOutputTokens: 8192,
    inputTokenCost: 0.002,
    outputTokenCost: 0.006
  }
};

export class CustomLLMProvider implements LLMProvider {
  public readonly name = 'CustomLLM';
  private client: any; // 您的LLM客户端
  private modelConfig: CustomLLMModelConfig;

  constructor(
    apiKey: string,
    modelId: keyof typeof CUSTOM_LLM_MODELS = 'custom-model-v1',
    baseURL?: string
  ) {
    // 初始化您的LLM客户端
    this.client = new YourLLMClient({
      apiKey,
      baseURL
    });
    
    this.modelConfig = CUSTOM_LLM_MODELS[modelId];
    if (!this.modelConfig) {
      throw new Error(`Unsupported CustomLLM model: ${modelId}`);
    }
  }

  /**
   * 估算消息的token数量
   * 这是最重要的方法，需要准确实现
   */
  async estimateTokens(messages: Message[]): Promise<number> {
    try {
      // 方法1: 如果您的LLM提供token计数API
      const formattedMessages = this.convertToCustomFormat(messages);
      const response = await this.client.countTokens(formattedMessages);
      return response.tokenCount;
      
    } catch (error) {
      // 方法2: 回退到字符数估算
      console.warn('Token counting failed, using fallback method:', error);
      
      const totalChars = messages.reduce((sum, msg) => {
        const content = typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content);
        return sum + content.length;
      }, 0);
      
      // 根据您的模型调整这个比例 (字符数 / token数)
      return Math.ceil(totalChars / 4); // 假设平均4个字符 = 1个token
    }
  }

  /**
   * 生成对话摘要
   */
  async generateSummary(messages: Message[], prompt: string): Promise<string> {
    try {
      const formattedMessages = this.convertToCustomFormat(messages);
      
      const response = await this.client.generateCompletion({
        messages: [
          { role: 'system', content: prompt },
          ...formattedMessages,
          { role: 'user', content: 'Please provide a comprehensive summary.' }
        ],
        maxTokens: Math.min(2000, this.modelConfig.maxOutputTokens),
        temperature: 0.1
      });

      const summary = response.content?.trim();
      if (!summary) {
        throw new Error('Empty summary generated');
      }

      return summary;
    } catch (error) {
      throw new Error(`CustomLLM summary generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 获取上下文窗口大小
   */
  getContextWindow(): number {
    return this.modelConfig.contextWindow;
  }

  /**
   * 获取最大输出token数
   */
  getMaxOutputTokens(): number {
    return this.modelConfig.maxOutputTokens;
  }

  /**
   * 转换消息格式
   */
  private convertToCustomFormat(messages: Message[]): any[] {
    return messages.map(msg => ({
      role: msg.role,
      content: typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content),
      timestamp: msg.timestamp
    }));
  }

  /**
   * 测试连接（可选但推荐）
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.client.generateCompletion({
        messages: [{ role: 'user', content: 'Hello' }],
        maxTokens: 5
      });
      return !!response.content;
    } catch (error) {
      console.error('CustomLLM connection test failed:', error);
      return false;
    }
  }

  /**
   * 获取可用模型列表
   */
  static getAvailableModels(): string[] {
    return Object.keys(CUSTOM_LLM_MODELS);
  }

  /**
   * 创建自定义模型配置
   */
  static createCustomModel(config: CustomLLMModelConfig): void {
    CUSTOM_LLM_MODELS[config.modelId] = config;
  }
}
```

### 2. 更新提供商工厂

在 `ProviderFactory.ts` 中添加新的提供商类型：

```typescript
// 添加到枚举
export enum ProviderType {
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic',
  GEMINI = 'gemini',
  CUSTOM_LLM = 'custom-llm', // 新增
  CUSTOM = 'custom'
}

// 在createProvider方法中添加case
case ProviderType.CUSTOM_LLM:
  provider = new CustomLLMProvider(
    config.apiKey,
    config.modelId as any,
    config.baseURL
  );
  break;

// 在getAvailableModels方法中添加
static getAvailableModels(): Record<ProviderType, string[]> {
  return {
    [ProviderType.OPENAI]: OpenAIProvider.getAvailableModels(),
    [ProviderType.ANTHROPIC]: AnthropicProvider.getAvailableModels(),
    [ProviderType.GEMINI]: GeminiProvider.getAvailableModels(),
    [ProviderType.CUSTOM_LLM]: CustomLLMProvider.getAvailableModels(), // 新增
    [ProviderType.CUSTOM]: []
  };
}
```

### 3. 更新主入口文件

在 `index.ts` 中导出新的提供商：

```typescript
// 添加导出
export { CustomLLMProvider } from './providers/CustomLLMProvider';

// 更新createCompressor函数的类型
export function createCompressor(
  providerConfig: {
    type: 'openai' | 'anthropic' | 'gemini' | 'custom-llm'; // 新增
    apiKey: string;
    modelId?: string;
    baseURL?: string;
  },
  compressionConfig?: Partial<CompressionConfig>
): ContextCompressor {
  // 添加case处理
  case 'custom-llm':
    providerType = ProviderType.CUSTOM_LLM;
    break;
}

// 添加到推荐配置
export const RECOMMENDED_PROVIDERS = {
  // ... 其他配置
  CUSTOM_OPTION: {
    type: 'custom-llm' as const,
    modelId: 'custom-model-v1',
    description: '自定义LLM提供商'
  }
};
```

## 🔧 关键实现要点

### 1. Token估算的准确性

Token估算是最关键的功能，直接影响压缩效果：

```typescript
async estimateTokens(messages: Message[]): Promise<number> {
  // 优先级1: 使用官方API
  try {
    return await this.client.countTokens(messages);
  } catch (error) {
    // 优先级2: 使用tokenizer库
    try {
      return await this.tokenizer.encode(messagesText).length;
    } catch (error) {
      // 优先级3: 字符数估算（最后的回退）
      return Math.ceil(totalChars / averageCharsPerToken);
    }
  }
}
```

### 2. 消息格式转换

不同LLM有不同的消息格式要求：

```typescript
private convertToCustomFormat(messages: Message[]): CustomMessage[] {
  return messages.map(msg => {
    // 处理系统消息
    if (msg.role === 'system') {
      return { type: 'system', text: msg.content };
    }
    
    // 处理多模态内容
    if (Array.isArray(msg.content)) {
      return {
        role: msg.role,
        content: msg.content.map(item => {
          if (item.type === 'image') {
            return { type: 'image', data: item.data };
          }
          return { type: 'text', text: item.text };
        })
      };
    }
    
    return { role: msg.role, content: msg.content };
  });
}
```

### 3. 错误处理

实现健壮的错误处理：

```typescript
async generateSummary(messages: Message[], prompt: string): Promise<string> {
  try {
    const response = await this.client.generateSummary(messages, prompt);
    
    if (!response || !response.content) {
      throw new Error('Empty response from LLM');
    }
    
    return response.content.trim();
    
  } catch (error) {
    // 记录详细错误信息
    console.error('Summary generation failed:', {
      error: error.message,
      modelId: this.modelConfig.modelId,
      messageCount: messages.length
    });
    
    // 抛出用户友好的错误
    throw new Error(`${this.name} summary generation failed: ${error.message}`);
  }
}
```

## 🧪 测试新提供商

创建测试文件验证实现：

```typescript
// src/context-compressor/__tests__/CustomLLMProvider.test.ts
import { CustomLLMProvider } from '../providers/CustomLLMProvider';

describe('CustomLLMProvider', () => {
  let provider: CustomLLMProvider;

  beforeEach(() => {
    provider = new CustomLLMProvider('test-api-key');
  });

  test('should estimate tokens correctly', async () => {
    const messages = [
      { role: 'user', content: 'Hello world' }
    ];
    
    const tokens = await provider.estimateTokens(messages);
    expect(tokens).toBeGreaterThan(0);
  });

  test('should generate summary', async () => {
    const messages = [
      { role: 'user', content: 'Test message' },
      { role: 'assistant', content: 'Test response' }
    ];
    
    const summary = await provider.generateSummary(messages, 'Summarize this conversation');
    expect(summary).toBeTruthy();
    expect(typeof summary).toBe('string');
  });
});
```

## 📝 最佳实践

1. **准确的Token计算**: 这是压缩器正常工作的基础
2. **错误处理**: 实现完善的错误处理和回退机制
3. **性能优化**: 考虑缓存、批处理等优化策略
4. **文档完善**: 提供清晰的使用文档和示例
5. **测试覆盖**: 编写全面的单元测试

## 🔗 相关资源

- [LLMProvider接口定义](./ContextCompressor.ts)
- [现有提供商实现示例](./providers/)
- [使用示例](./examples/)
- [单元测试示例](./__tests__)

通过遵循这个指南，您可以轻松地为任何LLM添加上下文压缩支持！
