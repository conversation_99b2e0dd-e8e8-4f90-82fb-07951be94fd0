/**
 * 通用上下文压缩器 - 主入口文件
 * 
 * 这个模块提供了一个强大、灵活的LLM上下文压缩解决方案，
 * 支持多种LLM提供商，有效管理token消耗，保持对话连续性。
 * 
 * <AUTHOR> Name
 * @version 1.0.0
 */

// 核心类和接口
export {
  ContextCompressor,
  type Message,
  type LLMProvider,
  type CompressionConfig,
  type CompressionResult,
  type CompressionStats
} from './ContextCompressor';

// 提供商工厂
export {
  ProviderFactory,
  ProviderType,
  type ProviderConfig
} from './ProviderFactory';

// 具体提供商实现
export { OpenAIProvider } from './providers/OpenAIProvider';
export { AnthropicProvider } from './providers/AnthropicProvider';
export { GeminiProvider } from './providers/GeminiProvider';

// 使用示例（可选导出）
export {
  basicUsageExample,
  advancedConfigExample,
  multiProviderComparisonExample,
  performanceTestExample,
  runAllExamples
} from './examples/usage-example';

/**
 * 快速创建压缩器的便捷函数
 */
export function createCompressor(
  providerConfig: {
    type: 'openai' | 'anthropic' | 'gemini';
    apiKey: string;
    modelId?: string;
    baseURL?: string;
  },
  compressionConfig?: Partial<CompressionConfig>
): ContextCompressor {
  let providerType: ProviderType;

  switch (providerConfig.type) {
    case 'openai':
      providerType = ProviderType.OPENAI;
      break;
    case 'anthropic':
      providerType = ProviderType.ANTHROPIC;
      break;
    case 'gemini':
      providerType = ProviderType.GEMINI;
      break;
    default:
      throw new Error(`Unsupported provider type: ${providerConfig.type}`);
  }

  const provider = ProviderFactory.createProvider({
    type: providerType,
    apiKey: providerConfig.apiKey,
    modelId: providerConfig.modelId,
    baseURL: providerConfig.baseURL
  });

  return new ContextCompressor(provider, compressionConfig);
}

/**
 * 预设配置
 */
export const PRESET_CONFIGS = {
  /**
   * 成本优化配置 - 使用便宜的模型，激进的压缩策略
   */
  COST_OPTIMIZED: {
    compressionThreshold: 60,
    bufferPercentage: 15,
    keepRecentMessages: 3,
    preferIntelligentCompression: true,
    fallbackToSlidingWindow: true,
    slidingWindowRemovalRatio: 0.6
  } as Partial<CompressionConfig>,

  /**
   * 质量优化配置 - 保守的压缩策略，保留更多上下文
   */
  QUALITY_OPTIMIZED: {
    compressionThreshold: 85,
    bufferPercentage: 10,
    keepRecentMessages: 5,
    preferIntelligentCompression: true,
    fallbackToSlidingWindow: true,
    slidingWindowRemovalRatio: 0.3
  } as Partial<CompressionConfig>,

  /**
   * 平衡配置 - 在成本和质量之间取得平衡
   */
  BALANCED: {
    compressionThreshold: 75,
    bufferPercentage: 12,
    keepRecentMessages: 4,
    preferIntelligentCompression: true,
    fallbackToSlidingWindow: true,
    slidingWindowRemovalRatio: 0.5
  } as Partial<CompressionConfig>,

  /**
   * 开发环境配置 - 更激进的压缩，节省开发成本
   */
  DEVELOPMENT: {
    compressionThreshold: 50,
    bufferPercentage: 20,
    keepRecentMessages: 2,
    preferIntelligentCompression: true,
    fallbackToSlidingWindow: true,
    slidingWindowRemovalRatio: 0.7
  } as Partial<CompressionConfig>,

  /**
   * 生产环境配置 - 保守的压缩，确保服务质量
   */
  PRODUCTION: {
    compressionThreshold: 90,
    bufferPercentage: 8,
    keepRecentMessages: 6,
    preferIntelligentCompression: true,
    fallbackToSlidingWindow: true,
    slidingWindowRemovalRatio: 0.2
  } as Partial<CompressionConfig>
};

/**
 * 推荐的提供商配置
 */
export const RECOMMENDED_PROVIDERS = {
  /**
   * 最佳性价比 - Gemini 1.5 Flash 8B
   */
  BEST_VALUE: {
    type: 'gemini' as const,
    modelId: 'gemini-1.5-flash-8b',
    description: '最佳性价比选择，超低成本，1M上下文窗口'
  },

  /**
   * 最高质量 - Anthropic Claude 3.5 Sonnet
   */
  HIGHEST_QUALITY: {
    type: 'anthropic' as const,
    modelId: 'claude-3-5-sonnet-20241022',
    description: '最高质量的摘要生成，适合重要对话'
  },

  /**
   * 最低成本 - Gemini 1.5 Flash 8B
   */
  LOWEST_COST: {
    type: 'gemini' as const,
    modelId: 'gemini-1.5-flash-8b',
    description: '最经济的选择，适合高频压缩场景'
  },

  /**
   * 大上下文 - Gemini 1.5 Pro (2M context)
   */
  LARGE_CONTEXT: {
    type: 'gemini' as const,
    modelId: 'gemini-1.5-pro',
    description: '支持最大上下文窗口(2M tokens)，适合超长对话'
  },

  /**
   * 平衡选择 - Gemini 1.5 Flash
   */
  BALANCED: {
    type: 'gemini' as const,
    modelId: 'gemini-1.5-flash',
    description: '性能与成本的平衡选择，1M上下文窗口'
  }
};

/**
 * 工具函数：根据使用场景推荐配置
 */
export function getRecommendedConfig(scenario: 'chatbot' | 'code-assistant' | 'customer-service' | 'creative-writing'): {
  provider: typeof RECOMMENDED_PROVIDERS[keyof typeof RECOMMENDED_PROVIDERS];
  config: Partial<CompressionConfig>;
} {
  switch (scenario) {
    case 'chatbot':
      return {
        provider: RECOMMENDED_PROVIDERS.BEST_VALUE,
        config: {
          ...PRESET_CONFIGS.BALANCED,
          customSummaryPrompt: `
            Summarize the conversation focusing on:
            - User's current question or request
            - Key context needed to continue helping
            - Any preferences or constraints mentioned
            - Conversation tone and style
          `
        }
      };

    case 'code-assistant':
      return {
        provider: RECOMMENDED_PROVIDERS.HIGHEST_QUALITY,
        config: {
          ...PRESET_CONFIGS.QUALITY_OPTIMIZED,
          customSummaryPrompt: `
            Summarize the coding conversation including:
            - Programming language and framework being used
            - Current task or problem being solved
            - Code snippets and solutions discussed
            - Any specific requirements or constraints
            - Error messages or debugging context
          `
        }
      };

    case 'customer-service':
      return {
        provider: RECOMMENDED_PROVIDERS.BEST_VALUE,
        config: {
          ...PRESET_CONFIGS.PRODUCTION,
          customSummaryPrompt: `
            Summarize the customer service conversation including:
            - Customer's issue or request
            - Steps taken to resolve the problem
            - Current status of the issue
            - Any follow-up actions needed
            - Customer satisfaction level
          `
        }
      };

    case 'creative-writing':
      return {
        provider: RECOMMENDED_PROVIDERS.LARGE_CONTEXT,
        config: {
          ...PRESET_CONFIGS.QUALITY_OPTIMIZED,
          compressionThreshold: 90, // 更保守，保留创意上下文
          customSummaryPrompt: `
            Summarize the creative writing session including:
            - Story/content theme and genre
            - Characters and setting details
            - Plot points and narrative structure
            - Writing style and tone preferences
            - Current writing goals and direction
          `
        }
      };

    default:
      return {
        provider: RECOMMENDED_PROVIDERS.BEST_VALUE,
        config: PRESET_CONFIGS.BALANCED
      };
  }
}

/**
 * 版本信息
 */
export const VERSION = '1.0.0';

/**
 * 默认导出：便捷的创建函数
 */
export default createCompressor;
