import { EventEmitter } from 'events';

/**
 * 消息接口定义
 */
export interface Message {
  role: 'system' | 'user' | 'assistant';
  content: string | Array<{ type: string; text?: string; [key: string]: any }>;
  timestamp?: number;
  isSummary?: boolean;
  metadata?: Record<string, any>;
}

/**
 * LLM提供商接口
 */
export interface LLMProvider {
  name: string;
  estimateTokens(messages: Message[]): Promise<number>;
  generateSummary(messages: Message[], prompt: string): Promise<string>;
  getContextWindow(): number;
  getMaxOutputTokens(): number;
}

/**
 * 压缩配置接口
 */
export interface CompressionConfig {
  // 基础配置
  autoCompress: boolean;
  compressionThreshold: number; // 触发压缩的上下文使用百分比
  bufferPercentage: number; // 预留缓冲区百分比
  
  // 消息保留策略
  keepRecentMessages: number; // 保留最近N条消息
  keepFirstMessage: boolean; // 是否保留第一条消息
  
  // 压缩策略
  preferIntelligentCompression: boolean; // 优先使用智能压缩
  fallbackToSlidingWindow: boolean; // 智能压缩失败时回退到滑动窗口
  slidingWindowRemovalRatio: number; // 滑动窗口删除比例
  
  // 自定义选项
  customSummaryPrompt?: string;
  summaryProvider?: LLMProvider; // 专用于摘要的LLM提供商
  
  // 高级配置
  profileThresholds?: Record<string, number>; // 配置文件级别的阈值
  enableTelemetry: boolean;
}

/**
 * 压缩结果接口
 */
export interface CompressionResult {
  messages: Message[];
  summary?: string;
  tokensRemoved: number;
  tokensAfterCompression: number;
  compressionRatio: number;
  method: 'none' | 'intelligent' | 'sliding-window';
  cost?: number;
  error?: string;
}

/**
 * 压缩统计接口
 */
export interface CompressionStats {
  totalCompressions: number;
  intelligentCompressions: number;
  slidingWindowCompressions: number;
  totalTokensSaved: number;
  averageCompressionRatio: number;
  totalCost: number;
}

/**
 * 通用上下文压缩器类
 */
export class ContextCompressor extends EventEmitter {
  private config: CompressionConfig;
  private provider: LLMProvider;
  private stats: CompressionStats;
  
  // 默认摘要提示模板
  private static readonly DEFAULT_SUMMARY_PROMPT = `
You are tasked with creating a comprehensive summary of the conversation history.
Your summary should preserve all critical information needed to continue the conversation effectively.

Structure your summary as follows:

## Conversation Overview
Provide a high-level overview of the entire conversation flow and main topics discussed.

## Current Context
Detail what is currently being worked on or discussed, with emphasis on recent messages.

## Key Information
- Technical concepts, frameworks, and technologies mentioned
- Important decisions made or conclusions reached
- Specific files, code snippets, or resources referenced
- Problems identified and solutions attempted

## Action Items & Next Steps
List any pending tasks, unresolved issues, or planned next steps mentioned in the conversation.

## Critical Details
Include any specific details, configurations, or requirements that would be essential for continuing the work.

Provide only the summary without additional commentary.
`;

  constructor(provider: LLMProvider, config: Partial<CompressionConfig> = {}) {
    super();
    
    this.provider = provider;
    this.config = this.mergeWithDefaults(config);
    this.stats = this.initializeStats();
    
    this.emit('initialized', { provider: provider.name, config: this.config });
  }

  /**
   * 合并用户配置与默认配置
   */
  private mergeWithDefaults(userConfig: Partial<CompressionConfig>): CompressionConfig {
    return {
      autoCompress: true,
      compressionThreshold: 80,
      bufferPercentage: 10,
      keepRecentMessages: 3,
      keepFirstMessage: true,
      preferIntelligentCompression: true,
      fallbackToSlidingWindow: true,
      slidingWindowRemovalRatio: 0.5,
      enableTelemetry: true,
      ...userConfig
    };
  }

  /**
   * 初始化统计数据
   */
  private initializeStats(): CompressionStats {
    return {
      totalCompressions: 0,
      intelligentCompressions: 0,
      slidingWindowCompressions: 0,
      totalTokensSaved: 0,
      averageCompressionRatio: 0,
      totalCost: 0
    };
  }

  /**
   * 主要压缩方法
   */
  async compress(messages: Message[], profileId?: string): Promise<CompressionResult> {
    try {
      const initialTokens = await this.provider.estimateTokens(messages);
      const contextWindow = this.provider.getContextWindow();
      const maxOutputTokens = this.provider.getMaxOutputTokens();
      
      // 计算可用token数量
      const bufferTokens = Math.floor(contextWindow * (this.config.bufferPercentage / 100));
      const availableTokens = contextWindow - maxOutputTokens - bufferTokens;
      
      // 获取有效阈值
      const effectiveThreshold = this.getEffectiveThreshold(profileId);
      const thresholdTokens = Math.floor(contextWindow * (effectiveThreshold / 100));
      
      // 判断是否需要压缩
      if (!this.shouldCompress(initialTokens, thresholdTokens, availableTokens)) {
        return {
          messages,
          tokensRemoved: 0,
          tokensAfterCompression: initialTokens,
          compressionRatio: 1.0,
          method: 'none'
        };
      }

      this.emit('compressionStarted', { 
        initialTokens, 
        threshold: effectiveThreshold,
        method: this.config.preferIntelligentCompression ? 'intelligent' : 'sliding-window'
      });

      let result: CompressionResult;

      // 尝试智能压缩
      if (this.config.preferIntelligentCompression) {
        result = await this.intelligentCompress(messages, initialTokens);
        
        // 如果智能压缩失败且启用回退，使用滑动窗口
        if (result.error && this.config.fallbackToSlidingWindow) {
          this.emit('compressionFallback', { error: result.error });
          result = await this.slidingWindowCompress(messages, initialTokens);
        }
      } else {
        // 直接使用滑动窗口压缩
        result = await this.slidingWindowCompress(messages, initialTokens);
      }

      // 更新统计信息
      this.updateStats(result);
      
      this.emit('compressionCompleted', result);
      
      return result;

    } catch (error) {
      const errorResult: CompressionResult = {
        messages,
        tokensRemoved: 0,
        tokensAfterCompression: await this.provider.estimateTokens(messages),
        compressionRatio: 1.0,
        method: 'none',
        error: error instanceof Error ? error.message : 'Unknown compression error'
      };
      
      this.emit('compressionError', errorResult);
      return errorResult;
    }
  }

  /**
   * 智能压缩实现
   */
  private async intelligentCompress(messages: Message[], initialTokens: number): Promise<CompressionResult> {
    try {
      // 确定要摘要的消息范围
      const messagesToSummarize = this.getMessagesToSummarize(messages);
      
      if (messagesToSummarize.length <= 1) {
        throw new Error('Not enough messages to summarize');
      }

      // 生成摘要
      const summaryProvider = this.config.summaryProvider || this.provider;
      const summaryPrompt = this.config.customSummaryPrompt || ContextCompressor.DEFAULT_SUMMARY_PROMPT;
      
      const summary = await summaryProvider.generateSummary(messagesToSummarize, summaryPrompt);
      
      if (!summary.trim()) {
        throw new Error('Generated summary is empty');
      }

      // 构建压缩后的消息列表
      const compressedMessages = this.buildCompressedMessages(messages, summary);
      const finalTokens = await this.provider.estimateTokens(compressedMessages);
      
      this.stats.intelligentCompressions++;
      
      return {
        messages: compressedMessages,
        summary,
        tokensRemoved: initialTokens - finalTokens,
        tokensAfterCompression: finalTokens,
        compressionRatio: finalTokens / initialTokens,
        method: 'intelligent'
      };

    } catch (error) {
      return {
        messages,
        tokensRemoved: 0,
        tokensAfterCompression: initialTokens,
        compressionRatio: 1.0,
        method: 'intelligent',
        error: error instanceof Error ? error.message : 'Intelligent compression failed'
      };
    }
  }

  /**
   * 滑动窗口压缩实现
   */
  private async slidingWindowCompress(messages: Message[], initialTokens: number): Promise<CompressionResult> {
    const compressedMessages = this.truncateMessages(messages, this.config.slidingWindowRemovalRatio);
    const finalTokens = await this.provider.estimateTokens(compressedMessages);
    
    this.stats.slidingWindowCompressions++;
    
    return {
      messages: compressedMessages,
      tokensRemoved: initialTokens - finalTokens,
      tokensAfterCompression: finalTokens,
      compressionRatio: finalTokens / initialTokens,
      method: 'sliding-window'
    };
  }

  /**
   * 判断是否需要压缩
   */
  private shouldCompress(currentTokens: number, thresholdTokens: number, availableTokens: number): boolean {
    return this.config.autoCompress && (currentTokens >= thresholdTokens || currentTokens > availableTokens);
  }

  /**
   * 获取有效的压缩阈值
   */
  private getEffectiveThreshold(profileId?: string): number {
    if (profileId && this.config.profileThresholds?.[profileId] !== undefined) {
      const profileThreshold = this.config.profileThresholds[profileId];
      return profileThreshold === -1 ? this.config.compressionThreshold : profileThreshold;
    }
    return this.config.compressionThreshold;
  }

  /**
   * 获取需要摘要的消息
   */
  private getMessagesToSummarize(messages: Message[]): Message[] {
    const keepCount = this.config.keepRecentMessages;
    const messagesToKeep = messages.slice(-keepCount);
    
    // 检查保留的消息中是否已有摘要
    const hasRecentSummary = messagesToKeep.some(msg => msg.isSummary);
    if (hasRecentSummary) {
      throw new Error('Recent summary already exists');
    }
    
    return messages.slice(0, -keepCount);
  }

  /**
   * 构建压缩后的消息列表
   */
  private buildCompressedMessages(originalMessages: Message[], summary: string): Message[] {
    const keepCount = this.config.keepRecentMessages;
    const recentMessages = originalMessages.slice(-keepCount);
    
    const summaryMessage: Message = {
      role: 'assistant',
      content: summary,
      timestamp: Date.now(),
      isSummary: true,
      metadata: { compressionMethod: 'intelligent' }
    };

    const result: Message[] = [];
    
    // 保留第一条消息（如果配置要求）
    if (this.config.keepFirstMessage && originalMessages.length > 0) {
      result.push(originalMessages[0]);
    }
    
    result.push(summaryMessage, ...recentMessages);
    
    return result;
  }

  /**
   * 滑动窗口截断消息
   */
  private truncateMessages(messages: Message[], removalRatio: number): Message[] {
    if (messages.length <= 1) return messages;
    
    const result: Message[] = [];
    
    // 保留第一条消息
    if (this.config.keepFirstMessage) {
      result.push(messages[0]);
    }
    
    // 计算要删除的消息数量（确保为偶数以保持对话结构）
    const startIndex = this.config.keepFirstMessage ? 1 : 0;
    const availableMessages = messages.length - startIndex - this.config.keepRecentMessages;
    const rawRemovalCount = Math.floor(availableMessages * removalRatio);
    const removalCount = rawRemovalCount - (rawRemovalCount % 2);
    
    // 添加中间保留的消息
    const middleStart = startIndex + removalCount;
    const middleEnd = messages.length - this.config.keepRecentMessages;
    result.push(...messages.slice(middleStart, middleEnd));
    
    // 添加最近的消息
    result.push(...messages.slice(-this.config.keepRecentMessages));
    
    return result;
  }

  /**
   * 更新统计信息
   */
  private updateStats(result: CompressionResult): void {
    if (result.method === 'none') return;
    
    this.stats.totalCompressions++;
    this.stats.totalTokensSaved += result.tokensRemoved;
    
    // 更新平均压缩比
    const totalRatio = this.stats.averageCompressionRatio * (this.stats.totalCompressions - 1) + result.compressionRatio;
    this.stats.averageCompressionRatio = totalRatio / this.stats.totalCompressions;
    
    if (result.cost) {
      this.stats.totalCost += result.cost;
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): CompressionStats {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = this.initializeStats();
    this.emit('statsReset');
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<CompressionConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }

  /**
   * 获取当前配置
   */
  getConfig(): CompressionConfig {
    return { ...this.config };
  }
}
