{"name": "roo-cline", "displayName": "%extension.displayName%", "description": "%extension.description%", "publisher": "RooVeterinaryInc", "version": "3.25.6", "icon": "assets/icons/icon.png", "galleryBanner": {"color": "#617A91", "theme": "dark"}, "engines": {"vscode": "^1.84.0", "node": "20.19.2"}, "author": {"name": "Roo Code"}, "repository": {"type": "git", "url": "https://github.com/RooCodeInc/Roo-Code"}, "homepage": "https://github.com/RooCodeInc/Roo-Code", "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Education", "Snippets", "Testing"], "keywords": ["cline", "claude", "dev", "mcp", "openrouter", "coding", "agent", "autonomous", "chatgpt", "sonnet", "ai", "llama", "roo code", "roocode"], "activationEvents": ["onLanguage", "onStartupFinished"], "main": "./dist/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "roo-cline-ActivityBar", "title": "%views.activitybar.title%", "icon": "assets/icons/icon.svg"}]}, "views": {"roo-cline-ActivityBar": [{"type": "webview", "id": "roo-cline.SidebarProvider", "name": "%views.sidebar.name%"}]}, "commands": [{"command": "roo-cline.plusButtonClicked", "title": "%command.newTask.title%", "icon": "$(add)"}, {"command": "roo-cline.prompts<PERSON><PERSON>onClicked", "title": "%command.prompts.title%", "icon": "$(organization)"}, {"command": "roo-cline.mcpButtonClicked", "title": "%command.mcpServers.title%", "icon": "$(server)"}, {"command": "roo-cline.historyButtonClicked", "title": "%command.history.title%", "icon": "$(history)"}, {"command": "roo-cline.marketplaceButtonClicked", "title": "%command.marketplace.title%", "icon": "$(extensions)"}, {"command": "roo-cline.popoutButtonClicked", "title": "%command.openInEditor.title%", "icon": "$(link-external)"}, {"command": "roo-cline.accountButtonClicked", "title": "Account", "icon": "$(account)"}, {"command": "roo-cline.settingsButtonClicked", "title": "%command.settings.title%", "icon": "$(settings-gear)"}, {"command": "roo-cline.openInNewTab", "title": "%command.openInNewTab.title%", "category": "%configuration.title%"}, {"command": "roo-cline.explainCode", "title": "%command.explainCode.title%", "category": "%configuration.title%"}, {"command": "roo-cline.fixCode", "title": "%command.fixCode.title%", "category": "%configuration.title%"}, {"command": "roo-cline.improveCode", "title": "%command.improveCode.title%", "category": "%configuration.title%"}, {"command": "roo-cline.addToContext", "title": "%command.addToContext.title%", "category": "%configuration.title%"}, {"command": "roo-cline.newTask", "title": "%command.newTask.title%", "category": "%configuration.title%"}, {"command": "roo-cline.terminalAddToContext", "title": "%command.terminal.addToContext.title%", "category": "Terminal"}, {"command": "roo-cline.terminalFixCommand", "title": "%command.terminal.fixCommand.title%", "category": "Terminal"}, {"command": "roo-cline.terminalExplainCommand", "title": "%command.terminal.explainCommand.title%", "category": "Terminal"}, {"command": "roo-cline.setCustomStoragePath", "title": "%command.setCustomStoragePath.title%", "category": "%configuration.title%"}, {"command": "roo-cline.importSettings", "title": "%command.importSettings.title%", "category": "%configuration.title%"}, {"command": "roo-cline.focusInput", "title": "%command.focusInput.title%", "category": "%configuration.title%"}, {"command": "roo-cline.acceptInput", "title": "%command.acceptInput.title%", "category": "%configuration.title%"}], "menus": {"editor/context": [{"submenu": "roo-cline.contextMenu", "group": "1"}], "roo-cline.contextMenu": [{"command": "roo-cline.addToContext", "group": "1_actions@1"}, {"command": "roo-cline.explainCode", "group": "1_actions@2"}, {"command": "roo-cline.improveCode", "group": "1_actions@3"}], "terminal/context": [{"submenu": "roo-cline.terminalMenu", "group": "2"}], "roo-cline.terminalMenu": [{"command": "roo-cline.terminalAddToContext", "group": "1_actions@1"}, {"command": "roo-cline.terminalFixCommand", "group": "1_actions@2"}, {"command": "roo-cline.terminalExplainCommand", "group": "1_actions@3"}], "view/title": [{"command": "roo-cline.plusButtonClicked", "group": "navigation@1", "when": "view == roo-cline.SidebarProvider"}, {"command": "roo-cline.marketplaceButtonClicked", "group": "navigation@2", "when": "view == roo-cline.SidebarProvider"}, {"command": "roo-cline.settingsButtonClicked", "group": "navigation@3", "when": "view == roo-cline.SidebarProvider"}, {"command": "roo-cline.accountButtonClicked", "group": "navigation@4", "when": "view == roo-cline.SidebarProvider"}, {"command": "roo-cline.historyButtonClicked", "group": "overflow@1", "when": "view == roo-cline.SidebarProvider"}, {"command": "roo-cline.prompts<PERSON><PERSON>onClicked", "group": "overflow@2", "when": "view == roo-cline.SidebarProvider"}, {"command": "roo-cline.mcpButtonClicked", "group": "overflow@3", "when": "view == roo-cline.SidebarProvider"}, {"command": "roo-cline.popoutButtonClicked", "group": "overflow@4", "when": "view == roo-cline.SidebarProvider"}], "editor/title": [{"command": "roo-cline.plusButtonClicked", "group": "navigation@1", "when": "activeWebviewPanelId == roo-cline.TabPanelProvider"}, {"command": "roo-cline.marketplaceButtonClicked", "group": "navigation@2", "when": "activeWebviewPanelId == roo-cline.TabPanelProvider"}, {"command": "roo-cline.settingsButtonClicked", "group": "navigation@3", "when": "activeWebviewPanelId == roo-cline.TabPanelProvider"}, {"command": "roo-cline.accountButtonClicked", "group": "navigation@4", "when": "activeWebviewPanelId == roo-cline.TabPanelProvider"}, {"command": "roo-cline.historyButtonClicked", "group": "overflow@1", "when": "activeWebviewPanelId == roo-cline.TabPanelProvider"}, {"command": "roo-cline.prompts<PERSON><PERSON>onClicked", "group": "overflow@2", "when": "activeWebviewPanelId == roo-cline.TabPanelProvider"}, {"command": "roo-cline.mcpButtonClicked", "group": "overflow@3", "when": "activeWebviewPanelId == roo-cline.TabPanelProvider"}, {"command": "roo-cline.popoutButtonClicked", "group": "overflow@4", "when": "activeWebviewPanelId == roo-cline.TabPanelProvider"}]}, "submenus": [{"id": "roo-cline.contextMenu", "label": "%views.contextMenu.label%"}, {"id": "roo-cline.terminalMenu", "label": "%views.terminalMenu.label%"}], "configuration": {"title": "%configuration.title%", "properties": {"roo-cline.allowedCommands": {"type": "array", "items": {"type": "string"}, "default": ["npm test", "npm install", "tsc", "git log", "git diff", "git show"], "description": "%commands.allowedCommands.description%"}, "roo-cline.deniedCommands": {"type": "array", "items": {"type": "string"}, "default": [], "description": "%commands.deniedCommands.description%"}, "roo-cline.commandExecutionTimeout": {"type": "number", "default": 0, "minimum": 0, "maximum": 600, "description": "%commands.commandExecutionTimeout.description%"}, "roo-cline.commandTimeoutAllowlist": {"type": "array", "items": {"type": "string"}, "default": [], "description": "%commands.commandTimeoutAllowlist.description%"}, "roo-cline.preventCompletionWithOpenTodos": {"type": "boolean", "default": false, "description": "%commands.preventCompletionWithOpenTodos.description%"}, "roo-cline.vsCodeLmModelSelector": {"type": "object", "properties": {"vendor": {"type": "string", "description": "%settings.vsCodeLmModelSelector.vendor.description%"}, "family": {"type": "string", "description": "%settings.vsCodeLmModelSelector.family.description%"}}, "description": "%settings.vsCodeLmModelSelector.description%"}, "roo-cline.customStoragePath": {"type": "string", "default": "", "description": "%settings.customStoragePath.description%"}, "roo-cline.enableCodeActions": {"type": "boolean", "default": true, "description": "%settings.enableCodeActions.description%"}, "roo-cline.autoImportSettingsPath": {"type": "string", "default": "", "description": "%settings.autoImportSettingsPath.description%"}, "roo-cline.useAgentRules": {"type": "boolean", "default": true, "description": "%settings.useAgentRules.description%"}}}}, "scripts": {"lint": "eslint . --ext=ts --max-warnings=0", "check-types": "tsc --noEmit", "pretest": "turbo run bundle --cwd ..", "test": "vitest run", "format": "prettier --write .", "bundle": "node esbuild.mjs", "vscode:prepublish": "pnpm bundle --production", "vsix": "mkdirp ../bin && vsce package --no-dependencies --out ../bin", "publish:marketplace": "vsce publish --no-dependencies && ovsx publish --no-dependencies", "watch:bundle": "pnpm bundle --watch", "watch:tsc": "cd .. && tsc --noEmit --watch --project src/tsconfig.json", "clean": "rimraf README.md CHANGELOG.md LICENSE dist logs mock .turbo"}, "dependencies": {"@anthropic-ai/bedrock-sdk": "^0.10.2", "@anthropic-ai/sdk": "^0.37.0", "@anthropic-ai/vertex-sdk": "^0.7.0", "@aws-sdk/client-bedrock-runtime": "^3.848.0", "@aws-sdk/credential-providers": "^3.848.0", "@google/genai": "^1.0.0", "@lmstudio/sdk": "^1.1.1", "@mistralai/mistralai": "^1.3.6", "@modelcontextprotocol/sdk": "^1.9.0", "@qdrant/js-client-rest": "^1.14.0", "@roo-code/cloud": "^0.5.0", "@roo-code/ipc": "workspace:^", "@roo-code/telemetry": "workspace:^", "@roo-code/types": "workspace:^", "@types/lodash.debounce": "^4.0.9", "@vscode/codicons": "^0.0.36", "async-mutex": "^0.5.0", "axios": "^1.7.4", "cheerio": "^1.0.0", "chokidar": "^4.0.1", "clone-deep": "^4.0.1", "default-shell": "^2.2.0", "delay": "^6.0.0", "diff": "^5.2.0", "diff-match-patch": "^1.0.5", "exceljs": "^4.4.0", "fast-deep-equal": "^3.1.3", "fast-xml-parser": "^5.0.0", "fastest-levenshtein": "^1.0.16", "fzf": "^0.5.2", "get-folder-size": "^5.0.0", "google-auth-library": "^9.15.1", "gray-matter": "^4.0.3", "i18next": "^25.0.0", "ignore": "^7.0.3", "isbinaryfile": "^5.0.2", "lodash.debounce": "^4.0.8", "mammoth": "^1.9.1", "monaco-vscode-textmate-theme-converter": "^0.1.7", "node-cache": "^5.1.2", "node-ipc": "^12.0.0", "openai": "^5.0.0", "os-name": "^6.0.0", "p-limit": "^6.2.0", "p-wait-for": "^5.0.2", "pdf-parse": "^1.1.1", "pkce-challenge": "^5.0.0", "pretty-bytes": "^7.0.0", "proper-lockfile": "^4.1.2", "ps-tree": "^1.2.0", "puppeteer-chromium-resolver": "^24.0.0", "puppeteer-core": "^23.4.0", "reconnecting-eventsource": "^1.6.4", "sanitize-filename": "^1.6.3", "say": "^0.16.0", "serialize-error": "^12.0.0", "simple-git": "^3.27.0", "sound-play": "^1.1.0", "stream-json": "^1.8.0", "string-similarity": "^4.0.4", "strip-ansi": "^7.1.0", "strip-bom": "^5.0.0", "tiktoken": "^1.0.21", "tmp": "^0.2.3", "tree-sitter-wasms": "^0.1.12", "turndown": "^7.2.0", "uuid": "^11.1.0", "vscode-material-icons": "^0.1.1", "web-tree-sitter": "^0.25.6", "workerpool": "^9.2.0", "yaml": "^2.8.0", "zod": "^3.25.61"}, "devDependencies": {"@roo-code/build": "workspace:^", "@roo-code/config-eslint": "workspace:^", "@roo-code/config-typescript": "workspace:^", "@types/clone-deep": "^4.0.4", "@types/debug": "^4.1.12", "@types/diff": "^5.2.1", "@types/diff-match-patch": "^1.0.36", "@types/glob": "^8.1.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/node-cache": "^4.1.3", "@types/node-ipc": "^9.2.3", "@types/proper-lockfile": "^4.1.4", "@types/ps-tree": "^1.1.6", "@types/stream-json": "^1.7.8", "@types/string-similarity": "^4.0.2", "@types/tmp": "^0.2.6", "@types/turndown": "^5.0.5", "@types/vscode": "^1.84.0", "@vscode/test-electron": "^2.5.2", "@vscode/vsce": "3.3.2", "esbuild": "^0.25.0", "execa": "^9.5.2", "glob": "^11.0.1", "mkdirp": "^3.0.1", "nock": "^14.0.4", "npm-run-all2": "^8.0.1", "ovsx": "0.10.4", "rimraf": "^6.0.1", "tsup": "^8.4.0", "tsx": "^4.19.3", "typescript": "5.8.3", "vitest": "^3.2.3", "zod-to-ts": "^1.2.0"}}