{"extension.displayName": "Roo Code", "extension.description": "<PERSON><PERSON>t đội ngũ phát triển các tác nhân AI hoàn chỉnh trong trình soạn thảo của bạn.", "command.newTask.title": "<PERSON><PERSON><PERSON>", "command.explainCode.title": "<PERSON><PERSON><PERSON><PERSON>", "command.fixCode.title": "<PERSON><PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON><PERSON>", "command.addToContext.title": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>", "command.openInNewTab.title": "Mở trong Tab Mới", "command.focusInput.title": "<PERSON>ậ<PERSON>rung vào <PERSON>", "command.setCustomStoragePath.title": "Đặt Đường Dẫn Lưu Trữ Tùy Chỉnh", "command.importSettings.title": "<PERSON><PERSON><PERSON><PERSON> Đặt", "command.terminal.addToContext.title": "<PERSON><PERSON><PERSON>m <PERSON>i Dung Terminal vào Ngữ Cảnh", "command.terminal.fixCommand.title": "<PERSON><PERSON><PERSON>", "command.terminal.explainCommand.title": "<PERSON><PERSON><PERSON><PERSON>", "command.acceptInput.title": "<PERSON>ấ<PERSON>/<PERSON><PERSON><PERSON> Ý", "views.activitybar.title": "Roo Code", "views.contextMenu.label": "Roo Code", "views.terminalMenu.label": "Roo Code", "views.sidebar.name": "Roo Code", "command.mcpServers.title": "<PERSON><PERSON><PERSON> MCP", "command.prompts.title": "Chế Độ", "command.history.title": "<PERSON><PERSON><PERSON>", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "Mở trong Trình <PERSON>", "command.settings.title": "Cài Đặt", "command.documentation.title": "<PERSON><PERSON><PERSON>", "configuration.title": "Roo Code", "commands.allowedCommands.description": "<PERSON><PERSON><PERSON> l<PERSON>nh có thể được thực thi tự động khi 'Luôn phê duyệt các thao tác thực thi' đ<PERSON><PERSON><PERSON> bật", "commands.deniedCommands.description": "<PERSON><PERSON><PERSON> tiền tố lệnh sẽ được tự động từ chối mà không yêu cầu phê duyệt. Trong trường hợp xung đột với các lệnh được phép, việ<PERSON> khớp tiền tố dài nhất sẽ được ưu tiên. Thêm * để từ chối tất cả các lệnh.", "commands.commandExecutionTimeout.description": "Thời gian tối đa tính bằng giây để chờ việc thực thi lệnh hoàn thành trước khi hết thời gian chờ (0 = không có thời gian chờ, 1-600s, mặc định: 0s)", "commands.commandTimeoutAllowlist.description": "<PERSON><PERSON><PERSON> tiền tố lệnh được loại trừ khỏi thời gian chờ thực thi lệnh. <PERSON><PERSON><PERSON> lệnh khớp với những tiền tố này sẽ chạy mà không có giới hạn thời gian chờ.", "settings.vsCodeLmModelSelector.description": "Cài đặt cho API mô hình ngôn ngữ VSCode", "settings.vsCodeLmModelSelector.vendor.description": "<PERSON><PERSON><PERSON> cung cấp mô hình ngôn ngữ (ví dụ: copilot)", "settings.vsCodeLmModelSelector.family.description": "<PERSON><PERSON> mô hình ngôn ngữ (ví dụ: gpt-4)", "settings.customStoragePath.description": "Đường dẫn lưu trữ tùy chỉnh. Để trống để sử dụng vị trí mặc định. Hỗ trợ đường dẫn tuyệt đối (ví dụ: 'D:\\RooCodeStorage')", "settings.enableCodeActions.description": "<PERSON><PERSON><PERSON> sửa lỗi n<PERSON>h <PERSON>oo <PERSON>.", "settings.autoImportSettingsPath.description": "Đường dẫn đến tệp cấu hình RooCode để tự động nhập khi khởi động tiện ích mở rộng. Hỗ trợ đường dẫn tuyệt đối và đường dẫn tương đối đến thư mụ<PERSON> (ví dụ: '~/Documents/roo-code-settings.json'). Để trống để tắt tính năng tự động nhập.", "settings.useAgentRules.description": "<PERSON><PERSON><PERSON> t<PERSON>i tệp AGENTS.md cho các quy tắc dành riêng cho tác nhân (xem https://agent-rules.org/)"}