{"extension.displayName": "Roo Code", "extension.description": "在你的编辑器中提供完整的 AI 代理开发团队。", "command.newTask.title": "新建任务", "command.explainCode.title": "解释代码", "command.fixCode.title": "修复代码", "command.improveCode.title": "改进代码", "command.addToContext.title": "添加到上下文", "command.openInNewTab.title": "在新标签页中打开", "command.focusInput.title": "聚焦输入框", "command.setCustomStoragePath.title": "设置自定义存储路径", "command.importSettings.title": "导入设置", "command.terminal.addToContext.title": "将终端内容添加到上下文", "command.terminal.fixCommand.title": "修复此命令", "command.terminal.explainCommand.title": "解释此命令", "command.acceptInput.title": "接受输入/建议", "views.activitybar.title": "Roo Code", "views.contextMenu.label": "Roo Code", "views.terminalMenu.label": "Roo Code", "views.sidebar.name": "Roo Code", "command.mcpServers.title": "MCP 服务器", "command.prompts.title": "模式", "command.history.title": "历史记录", "command.marketplace.title": "应用市场", "command.openInEditor.title": "在编辑器中打开", "command.settings.title": "设置", "command.documentation.title": "文档", "configuration.title": "Roo Code", "commands.allowedCommands.description": "当启用'始终批准执行操作'时可以自动执行的命令", "commands.deniedCommands.description": "将自动拒绝而无需请求批准的命令前缀。与允许命令冲突时，最长前缀匹配优先。添加 * 拒绝所有命令。", "commands.commandExecutionTimeout.description": "等待命令执行完成的最大时间（秒），超时前（0 = 无超时，1-600秒，默认：0秒）", "commands.commandTimeoutAllowlist.description": "从命令执行超时中排除的命令前缀。匹配这些前缀的命令将在没有超时限制的情况下运行。", "settings.vsCodeLmModelSelector.description": "VSCode 语言模型 API 的设置", "settings.vsCodeLmModelSelector.vendor.description": "语言模型的供应商（例如：copilot）", "settings.vsCodeLmModelSelector.family.description": "语言模型的系列（例如：gpt-4）", "settings.customStoragePath.description": "自定义存储路径。留空以使用默认位置。支持绝对路径（例如：'D:\\RooCodeStorage'）", "settings.enableCodeActions.description": "启用 Roo Code 快速修复", "settings.autoImportSettingsPath.description": "RooCode 配置文件的路径，用于在扩展启动时自动导入。支持绝对路径和相对于主目录的路径（例如 '~/Documents/roo-code-settings.json'）。留空以禁用自动导入。", "settings.useAgentRules.description": "为特定于代理的规则启用 AGENTS.md 文件的加载（请参阅 https://agent-rules.org/）"}