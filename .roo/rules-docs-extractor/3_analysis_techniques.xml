<analysis_techniques>
  <overview>
    Techniques for analyzing code to extract documentation.
  </overview>

  <ui_ux_analysis_techniques>
    <technique name="component_discovery">
      <description>
        Find and analyze UI components and their interactions
      </description>
      <discovery_methods>
        <method name="component_search">
          <description>Search for UI component files</description>
          <example><![CDATA[
<!-- Find React/Vue/Angular components -->
<search_files>
<path>src</path>
<regex>\.(tsx|jsx|vue)$|@Component|export.*component</regex>
<file_pattern>*.tsx</file_pattern>
</search_files>

<!-- Find component usage -->
<search_files>
<path>src</path>
<regex><Button|<Form|<Modal|<Dialog|<Input</regex>
</search_files>
          ]]></example>
        </method>
        
        <method name="style_analysis">
          <description>Analyze styling and visual elements</description>
          <example><![CDATA[
<!-- Find stylesheets -->
<list_files>
<path>src/styles</path>
<recursive>true</recursive>
</list_files>

<!-- Search for style definitions -->
<search_files>
<path>src</path>
<regex>className=|style=|styled\.|makeStyles|@apply</regex>
</search_files>
          ]]></example>
        </method>
      </discovery_methods>
    </technique>

    <technique name="user_flow_mapping">
      <description>
        Map user interactions and navigation flows
      </description>
      <analysis_areas>
        <area>Route definitions and navigation</area>
        <area>Form submissions and validations</area>
        <area>Button clicks and event handlers</area>
        <area>State changes and UI updates</area>
        <area>Loading and error states</area>
      </analysis_areas>
      <search_patterns><![CDATA[
<!-- Find route definitions -->
<search_files>
<path>src</path>
<regex>Route.*path=|router\.push|navigate\(|Link.*to=</regex>
</search_files>

<!-- Find event handlers -->
<search_files>
<path>src</path>
<regex>onClick=|onSubmit=|onChange=|handleClick|handleSubmit</regex>
</search_files>

<!-- Find form validations -->
<search_files>
<path>src</path>
<regex>validate|validation|required|pattern=|minLength|maxLength</regex>
</search_files>
      ]]></search_patterns>
    </technique>

    <technique name="user_feedback_analysis">
      <description>
        Analyze how the system communicates with users
      </description>
      <elements_to_find>
        <element>Error messages and alerts</element>
        <element>Success notifications</element>
        <element>Loading indicators</element>
        <element>Tooltips and help text</element>
        <element>Confirmation dialogs</element>
        <element>Progress indicators</element>
      </elements_to_find>
      <search_patterns><![CDATA[
<!-- Find user messages -->
<search_files>
<path>src</path>
<regex>toast|notification|alert|message|error.*message|success.*message</regex>
</search_files>

<!-- Find loading states -->
<search_files>
<path>src</path>
<regex>loading|isLoading|pending|spinner|skeleton|placeholder</regex>
</search_files>

<!-- Find dialogs and modals -->
<search_files>
<path>src</path>
<regex>modal|dialog|confirm|popup|overlay</regex>
</search_files>
      ]]></search_patterns>
    </technique>

    <technique name="accessibility_analysis">
      <description>
        Check for accessibility features and compliance
      </description>
      <accessibility_checks>
        <check>ARIA labels and roles</check>
        <check>Keyboard navigation support</check>
        <check>Screen reader compatibility</check>
        <check>Focus management</check>
        <check>Color contrast considerations</check>
      </accessibility_checks>
      <search_patterns><![CDATA[
<!-- Find accessibility attributes -->
<search_files>
<path>src</path>
<regex>aria-|role=|tabIndex|alt=|title=|accessibilityLabel</regex>
</search_files>

<!-- Find focus management -->
<search_files>
<path>src</path>
<regex>focus\(|blur\(|onFocus|onBlur|autoFocus|focusable</regex>
</search_files>
      ]]></search_patterns>
    </technique>

    <technique name="responsive_design_analysis">
      <description>
        Analyze responsive design and mobile experience
      </description>
      <analysis_points>
        <point>Breakpoint definitions</point>
        <point>Mobile-specific components</point>
        <point>Touch event handlers</point>
        <point>Viewport configurations</point>
        <point>Media queries</point>
      </analysis_points>
      <search_patterns><![CDATA[
<!-- Find responsive utilities -->
<search_files>
<path>src</path>
<regex>@media|breakpoint|mobile|tablet|desktop|responsive</regex>
</search_files>

<!-- Find touch events -->
<search_files>
<path>src</path>
<regex>onTouch|swipe|gesture|tap|press</regex>
</search_files>
      ]]></search_patterns>
    </technique>
  </ui_ux_analysis_techniques>

  <code_analysis_techniques>
    <technique name="semantic_search_analysis">
      <description>
        Use semantic search to find conceptually related code when available.
      </description>
      <when_to_use>
        <scenario>Finding code by concept rather than keywords</scenario>
        <scenario>Discovering implementations across different naming conventions</scenario>
        <scenario>When pattern-based search isn't finding expected results</scenario>
      </when_to_use>
      <example><![CDATA[
<!-- Optional: Find authentication-related code semantically -->
<codebase_search>
<query>user authentication login security JWT token validation</query>
</codebase_search>

<!-- Optional: Find payment processing logic -->
<codebase_search>
<query>payment processing transaction billing invoice checkout</query>
</codebase_search>
      ]]></example>
      <note>This is an optional tool - use when semantic understanding would help find related code that keyword search might miss</note>
    </technique>

    <technique name="entry_point_analysis">
      <description>
        Analyze entry points to understand feature flow.
      </description>
      <steps>
        <step>Find main functions, controllers, or route handlers.</step>
        <step>Trace execution flow.</step>
        <step>Map decision branches.</step>
        <step>Document input validation.</step>
      </steps>
      <discovery_methods>
        <method name="directory_based">
          <description>Start by exploring directory structure</description>
          <example><![CDATA[
<!-- List main directories -->
<list_files>
<path>src</path>
<recursive>false</recursive>
</list_files>

<!-- Explore feature directory -->
<list_files>
<path>src/controllers</path>
<recursive>true</recursive>
</list_files>
          ]]></example>
        </method>
        
        <method name="pattern_based">
          <description>Search for specific patterns</description>
          <example><![CDATA[
<!-- Find all routes -->
<search_files>
<path>src</path>
<regex>(app\.(get|post|put|delete)|@(Get|Post|Put|Delete)|router\.(get|post|put|delete))</regex>
</search_files>
          ]]></example>
        </method>
        
        <method name="file_based">
          <description>Read known entry points directly</description>
          <example><![CDATA[
<!-- Read main application file -->
<read_file>
<path>src/app.ts</path>
</read_file>

<!-- Read specific controller -->
<read_file>
<path>src/controllers/feature.controller.ts</path>
</read_file>
          ]]></example>
        </method>
        
        <method name="semantic_based">
          <description>Use semantic search as an alternative discovery method</description>
          <example><![CDATA[
<!-- Optional: Find entry points semantically -->
<codebase_search>
<query>main entry point application startup initialization bootstrap</query>
</codebase_search>
          ]]></example>
        </method>
      </discovery_methods>
    </technique>

    <technique name="api_extraction">
      <description>
        Extract API specifications from code.
      </description>
      <patterns>
        <pattern type="rest">
          <search_regex><![CDATA[
(app|router)\.(get|post|put|patch|delete)\s*\(\s*['"`]([^'"`]+)['"`]
          ]]></search_regex>
          <extraction>
            - HTTP method
            - Route path
            - Path/query parameters
            - Request/response schemas
            - Status codes
          </extraction>
        </pattern>
        <pattern type="graphql">
          <search_regex><![CDATA[
type\s+(Query|Mutation|Subscription)\s*{[^}]+}|@(Query|Mutation|Resolver)
          ]]></search_regex>
          <extraction>
            - Schema and input types
            - Resolvers
            - Return types
            - Field arguments
          </extraction>
        </pattern>
      </patterns>
    </technique>

    <technique name="dependency_mapping">
      <description>
        Map dependencies and integration points.
      </description>
      <analysis_points>
        <point>Import/require statements</point>
        <point>package.json dependencies</point>
        <point>External API calls</point>
        <point>DB connections</point>
        <point>Message queue integrations</point>
        <point>Filesystem operations</point>
      </analysis_points>
      <analysis_approaches>
        <approach name="package_analysis">
          <description>Start with package.json to understand dependencies</description>
          <example><![CDATA[
<!-- Analyze package dependencies -->
<read_file>
<path>package.json</path>
</read_file>
          ]]></example>
        </approach>
        
        <approach name="import_tracking">
          <description>Follow import chains to map dependencies</description>
          <example><![CDATA[
<!-- Find all imports -->
<search_files>
<path>src</path>
<regex>^import\s+.*from\s+['"]([^'"]+)['"]|require\s*\(\s*['"]([^'"]+)['"]\s*\)</regex>
</search_files>
          ]]></example>
        </approach>
        
        <approach name="api_discovery">
          <description>Find external API integrations</description>
          <example><![CDATA[
<!-- Find external API calls -->
<search_files>
<path>src</path>
<regex>(fetch|axios|http\.request|request\(|\.get\(|\.post\()</regex>
</search_files>
          ]]></example>
        </approach>
      </analysis_approaches>
    </technique>

    <technique name="data_model_extraction">
      <description>
        Extract data models, schemas, and type definitions.
      </description>
      <sources>
        <source type="typescript">
          <patterns>
            - interfaces, types, classes, enums
          </patterns>
        </source>
        <source type="database">
          <patterns>
            - Schema definitions, migration files, ORM models
          </patterns>
        </source>
        <source type="validation">
          <patterns>
            - JSON Schema, Joi/Yup/Zod schemas, validation decorators
          </patterns>
        </source>
      </sources>
      <extraction_example><![CDATA[
<!-- Find TypeScript interfaces -->
<search_files>
<path>src</path>
<regex>^export\s+(interface|type|class|enum)\s+(\w+)</regex>
</search_files>

<!-- Find database models -->
<search_files>
<path>src/models</path>
<regex>@(Entity|Table|Model)|class\s+\w+\s+extends\s+(Model|BaseEntity)</regex>
</search_files>
      ]]></extraction_example>
    </technique>

    <technique name="business_logic_extraction">
      <description>
        Identify and document business rules.
      </description>
      <indicators>
        <indicator>Complex conditionals</indicator>
        <indicator>Calculation functions</indicator>
        <indicator>Validation rules</indicator>
        <indicator>State machines</indicator>
        <indicator>Domain-specific constants and algorithms</indicator>
      </indicators>
      <documentation_focus>
        <focus>Why logic exists (business need)</focus>
        <focus>When logic applies (conditions)</focus>
        <focus>What logic does (transformation)</focus>
        <focus>Edge cases</focus>
        <focus>Impact of changes</focus>
      </documentation_focus>
    </technique>

    <technique name="error_handling_analysis">
      <description>
        Document error handling and recovery.
      </description>
      <analysis_areas>
        <area>try/catch blocks, error boundaries</area>
        <area>Custom error classes</area>
        <area>Error codes and messages</area>
        <area>Logging, fallbacks, retries, circuit breakers</area>
      </analysis_areas>
      <search_patterns><![CDATA[
<!-- Find error handling -->
<search_files>
<path>src</path>
<regex>try\s*{|catch\s*\(|throw\s+new|class\s+\w*Error\s+extends</regex>
</search_files>

<!-- Find error constants -->
<search_files>
<path>src</path>
<regex>ERROR_|_ERROR|ErrorCode|errorCode</regex>
</search_files>
      ]]></search_patterns>
    </technique>

    <technique name="security_analysis">
      <description>
        Identify security measures and vulnerabilities.
      </description>
      <security_checks>
        <check category="authentication">
          <patterns>
            - JWT, sessions, OAuth, API keys
          </patterns>
        </check>
        <check category="authorization">
          <patterns>
            - RBAC, permission checks, ownership validation
          </patterns>
        </check>
        <check category="data_protection">
          <patterns>
            - Encryption, hashing, sensitive data handling
          </patterns>
        </check>
        <check category="input_validation">
          <patterns>
            - Sanitization, SQLi/XSS/CSRF prevention
          </parents>
        </check>
      </security_checks>
    </technique>

    <technique name="performance_analysis">
      <description>
        Identify performance factors and optimization opportunities.
      </description>
      <analysis_points>
        <point>DB query patterns (N+1)</point>
        <point>Caching strategies</point>
        <point>Async usage</point>
        <point>Batch processing</point>
        <point>Resource pooling</point>
        <point>Memory management</point>
        <point>Algorithm complexity</point>
      </analysis_points>
      <metrics_to_document>
        <metric>Time/space complexity</metric>
        <metric>DB query counts</metric>
        <metric>API response times</metric>
        <metric>Memory usage</metric>
        <metric>Concurrency handling</metric>
      </metrics_to_document>
    </technique>

    <technique name="test_coverage_analysis">
      <description>
        Analyze test coverage.
      </description>
      <test_types>
        <type name="unit">
          <location>__tests__, *.test.ts, *.spec.ts</location>
          <analysis>Function coverage</analysis>
        </type>
        <type name="integration">
          <location>integration/, e2e/</location>
          <analysis>Workflow coverage</analysis>
        </type>
        <type name="api">
          <location>api-tests/, *.api.test.ts</location>
          <analysis>Endpoint coverage</analysis>
        </type>
      </test_types>
      <coverage_analysis><![CDATA[
<!-- Find test files -->
<search_files>
<path>src</path>
<regex>\.(test|spec)\.(ts|js|tsx|jsx)$</regex>
<file_pattern>*.test.ts</file_pattern>
</search_files>

<!-- Analyze test descriptions -->
<search_files>
<path>src</path>
<regex>(describe|it|test)\s*\(\s*['"`]([^'"`]+)['"`]</regex>
</search_files>
      ]]></coverage_analysis>
    </technique>

    <technique name="configuration_extraction">
      <description>
        Extract configuration options and their impacts.
      </description>
      <configuration_sources>
        <source>.env files, config files, CLI args, feature flags</source>
      </configuration_sources>
      <documentation_requirements>
        <requirement>Default values</requirement>
        <requirement>Valid values</requirement>
        <requirement>Behavior impact</requirement>
        <requirement>Config dependencies</requirement>
        <requirement>Security implications</requirement>
      </documentation_requirements>
    </technique>
  </code_analysis_techniques>

  <workflow_analysis>
    <technique name="user_journey_mapping">
      <description>
        Map user workflows through the feature.
      </description>
      <steps>
        <step>Identify entry points (UI, API, CLI).</step>
        <step>Trace user actions.</step>
        <step>Document decision points.</step>
        <step>Map data transformations.</step>
        <step>Identify outcomes.</step>
      </steps>
      <deliverables>
        <deliverable>Flow diagrams, procedures, decision trees, state diagrams.</deliverable>
      </deliverables>
    </technique>

    <technique name="integration_flow_analysis">
      <description>
        Document integration with other systems.
      </description>
      <integration_types>
        <type>Sync API calls, async messaging, events, batch processing, streaming.</type>
      </integration_types>
      <documentation_focus>
        <focus>Protocols, auth, error handling, data transforms, SLAs.</focus>
      </documentation_focus>
    </technique>
  </workflow_analysis>

  <metadata_extraction>
    <technique name="version_compatibility">
      <sources>
        <source>package.json, READMEs, migration guides, breaking changes docs.</source>
      </sources>
      <extraction_pattern><![CDATA[
<!-- Find version requirements -->
<search_files>
<path>.</path>
<regex>"engines":|"peerDependencies":|requires?\s+\w+\s+version|compatible\s+with</regex>
</search_files>
      ]]></extraction_pattern>
    </technique>

    <technique name="deprecation_tracking">
      <indicators>
        <indicator>@deprecated, TODO comments, legacy code markers.</indicator>
      </indicators>
      <documentation_requirements>
        <requirement>Deprecation date, removal timeline, migration path, alternatives.</requirement>
      </documentation_requirements>
    </technique>
  </metadata_extraction>

  <quality_indicators>
    <indicator name="documentation_completeness">
      <checks>
        <check>Public APIs documented.</check>
        <check>Examples for complex features.</check>
        <check>Error scenarios covered.</check>
        <check>Config options explained.</check>
        <check>Security addressed.</check>
      </checks>
    </indicator>

    <indicator name="code_quality_metrics">
      <metrics>
        <metric>Cyclomatic complexity, code duplication, test coverage, doc coverage, tech debt.</metric>
      </metrics>
    </indicator>
  </quality_indicators>
</analysis_techniques>