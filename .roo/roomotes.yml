version: "1.0"

commands:
    - name: Pull latest changes
      run: git pull
      timeout: 60
      execution_phase: task_run
    - name: Install dependencies
      run: pnpm install
      timeout: 60
      execution_phase: task_run

github_events:
    - event: issues.opened
      action:
          name: github.issue.fix
    - event: issue_comment.created
      action:
          name: github.issue.comment.respond
    - event: pull_request.opened
      action:
          name: github.pr.review
    - event: pull_request.opened
      action:
          name: general.task
          prompt: |
              1. Run the script `node scripts/find-missing-translations.js` and carefully review its output for any missing translations.
              2. If the script reports missing translations, switch into `translate` mode and add them in all supported languages.
              3. If you've added new translations, commit and push them to the existing PR.
              4. If you get a permission error trying to push to the PR just give up (i.e don't create a new PR instead).
    - event: pull_request_review_comment.created
      action:
          name: github.pr.comment.respond
